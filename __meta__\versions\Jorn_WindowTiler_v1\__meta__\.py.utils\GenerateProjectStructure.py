import os
import sys
import time
import argparse

from rich import print as rprint

nums_list = [1, 2, 3, 4]
rprint(nums_list)

nums_tuple = (1, 2, 3, 4)
rprint(nums_tuple)

nums_dict = {'nums_list': nums_list, 'nums_tuple': nums_tuple}
rprint(nums_dict)

bool_list = [True, False]
rprint(bool_list)
from rich.console import Console

console = Console()


def merge_dict(dict_one, dict_two):
    merged_dict = dict_one | dict_two
    console.log(merged_dict, log_locals=True)


merge_dict({'id': 1}, {'name': 'Ashutosh'})
os.environ['USERPROFILE']
# 1. Run initialize -> Opens VENV_Create_New_Project.py
# 2. New_Project.py: Lists all existing projects in project-directory
# 2. New_Project.py:



# include:
# - venv-utils (file-to-copy)
# - init venv locally or externally (option)
# - generate request and/or wheels
# - wheel download/install

# type: project (private venv)
# venv: external / internal
# name: project-name

# type: environment (shared venv)
# venv: external / internal
# name: environment-name (e.g. PyEnv_ExcelSheets)



def prompt_for_project_path(default_path: str) -> str:
    """
    Prompts the user for the path to the project.
    """
    script_name = input('Specify the name of the project: ')
    return script_name

def prompt_for_project_name(default_name: str) -> str:
    """
    Prompts the user for the name of the project.
    """
    script_name = input('Specify the name of the project: ')
    return script_name

def create_bat_and_py_files(script_name: str):
    """
    Creates a bat file and a Python script with the specified name in the folder of the bat-script
    """
    # Define file directories
    bat_file_directory = os.path.join(sys.argv[1], f'{script_name}.bat')
    py_file_directory = os.path.join(sys.argv[1], f'{script_name}.py')

    # Increment name if files already exist
    i = 1
    while os.path.exists(bat_file_directory) or os.path.exists(py_file_directory):
        bat_file_directory = os.path.join(sys.argv[1], f'{script_name}_{i}.bat')
        py_file_directory = os.path.join(sys.argv[1], f'{script_name}_{i}.py')
        i += 1

    os.system('CLS')
    print(f'The resulting bat file will be saved as:\n{bat_file_directory}')
    print(f'The resulting Python file will be saved as:\n{py_file_directory}')

    # Get the script names (without path)
    bat_file_name = (str(py_file_directory.split('\\')[-1]))
    py_file_name = (str(py_file_directory.split('\\')[-1]))
    # Create content for bat file
    bat_file_content = f"""@ECHO OFF
:: Specify the paths
SET "PYTHON_PATH=venv/Scripts/python.exe"
SET "OUTPUT_PATH={(str(sys.argv[1]))}"
:: Execute script
%PYTHON_PATH% "{py_file_directory.split("/")[-1]}" %OUTPUT_PATH%
PAUSE""".replace('/','\\')

#     # Create content for bat file
#     bat_file_content = f"""@ECHO OFF
# :: Specify the paths
# SET "PYTHON_PATH=venv\Scripts\python.exe"
# SET "SCRIPT_NAME={script_name}.py"
# SET "OUTPUT_PATH={sys.argv[1]}"

# :: Execute script
# %PYTHON_PATH% %SCRIPT_NAME% %OUTPUT_PATH%
# :: WITH INPUT-ARGUMENT

# PAUSE"""

    # Create content for Python file
    py_file_content = """
import sys
import os
import time
import random
import json
import subprocess
import urllib.parse
import argparse
import configparser

def example_codeblock_1(example_input_data: list):
    # Demonstrates a simple loop through a list of strings.
    for x in example_input_data:
        print(x)
    time.sleep(99999)

def example_codeblock_2(example_input_data: list):
    # Demonstrates using the enumerate function to loop through a list and print the index of each item.
    for index, item in enumerate(example_input_data):
        print(index)
    time.sleep(99999)

def main():
    # Calls the example code blocks in a specific order.
    example_input_data_1 = ['test1', 'a', 'b']
    example_input_data_2 = []

    example_codeblock_1(example_input_data_1)
    example_codeblock_2(example_input_data_2)

# Run the main function when the script is executed
if __name__ == "__main__":
    main()
"""

    # Write bat file to file
    with open(bat_file_directory, 'w') as file:
        file.write(bat_file_content)

    # Write Python file to file
    with open(py_file_directory, 'w') as file:
        file.write(py_file_content)


prompted_project_path = prompt_for_project_path(default_path='test_project_1')
prompted_project_name = prompt_for_project_name()
create_bat_and_py_files(script_name=prompted_project_name)
