# Default
import os
import sys
import re
import time
import random
import ctypes
import urllib.parse
import json
import math
from enum import Enum

# Rich
from rich.console import Console
from rich.table import Table

# Import pywin32 modules
import win32com.client
import win32com.shell.shellcon as shellcon


import win32gui
import win32api
import win32process

from win32api import (
    MonitorFromWindow,
    GetMonitorInfo,
)

from win32gui import (
    IsWindowVisible,
    IsWindowEnabled,
    IsIconic,
    GetWindowText,
    GetClassName,
    GetWindowLong,
    EnumWindows,
    MoveWindow,
)
from win32con import (
    MONITOR_DEFAULTTONEAREST,
    GWL_STYLE,
    GWL_EXSTYLE,
)





def get_all_windows():
    windows = {}
    def enum_windows(hwnd, result):
        windows[hwnd] = {
            'enabled'   : IsWindowEnabled(hwnd),
            'visible'   : IsWindowVisible(hwnd),
            'minimized' : IsIconic(hwnd),
            'title'     : GetWindowText(hwnd),
            'class'     : Get<PERSON><PERSON><PERSON>ame(hwnd),
            'style'     : GetWindowLong(hwnd, GWL_STYLE),
            'ex_style'  : GetWindowLong(hwnd, GWL_EXSTYLE),
        }
    EnumWindows(enum_windows, [])
    return windows


def filter_windows(self, visible_only=True, normal_windows_only=True):
    filtered = {}
    for hwnd, attributes in self.windows.items():
        if visible_only and not attributes['visible']:
            continue
        if normal_windows_only:
            if not attributes['title']:
                continue
            if attributes['class'].startswith(("tooltips_class32", "Shell_TrayWnd")):
                continue

        filtered[hwnd] = attributes
    return filtered


def get_hwnd_monitor(hwnd):
    monitor_handle = MonitorFromWindow(hwnd, MONITOR_DEFAULTTONEAREST)
    monitor_info = GetMonitorInfo(monitor_handle)
    monitor_device = (monitor_info["Device"])
    monitor_index = "".join(filter(str.isdigit, monitor_device))
    monitor_index = int(monitor_index) if monitor_index else None



print(get_all_windows())
