"""
Window Module for Window Tiler

This module provides functionality for detecting, manipulating, and filtering windows
in a Windows system. It implements a Window class with enhanced type detection and
various window operations.
"""

import win32gui
import win32con
import win32api

from window_tiler.core.types import WindowType, WindowState
from window_tiler.utils import determine_window_type, get_process_info, get_explorer_info


class Window:
    """
    Represents a window in the system with enhanced properties and manipulation methods.
    
    Attributes:
        hwnd: Window handle
        title: Window title
        class_name: Window class name
        dimensions: Window dimensions (x, y, width, height)
        window_type: Type of window (WindowType enum)
        process_info: Dictionary with process information
    """
    
    def __init__(self, hwnd, detect_type=True):
        """
        Initialize a Window object.
        
        Args:
            hwnd: Window handle
            detect_type: Whether to detect the window type on initialization
        """
        self.hwnd = hwnd
        self.title = win32gui.GetWindowText(hwnd)
        self.class_name = win32gui.GetClassName(hwnd)
        
        # Get window dimensions
        self.update_dimensions()
        
        # Initialize additional properties
        self.window_type = None
        self.process_info = None
        self.explorer_info = None
        
        # Detect window type if requested
        if detect_type:
            self.detect_type()

    def update_dimensions(self):
        """Update the window dimensions based on current position."""
        rect = win32gui.GetWindowRect(self.hwnd)
        self.dimensions = {
            'x': rect[0],
            'y': rect[1],
            'width': rect[2] - rect[0],
            'height': rect[3] - rect[1]
        }
        
    def detect_type(self):
        """Detect and set the window's type and associated information."""
        # Get process information
        self.process_info = get_process_info(self.hwnd)
        
        # Get Explorer-specific info for Explorer windows
        if self.class_name == 'CabinetWClass':
            self.explorer_info = get_explorer_info(self.hwnd)
            
        # Determine window type
        self.window_type = determine_window_type(self.hwnd)

    def get_state(self):
        """
        Get the current window state.
        
        Returns:
            WindowState: The window state
        """
        if not win32gui.IsWindowVisible(self.hwnd):
            return WindowState.HIDDEN
            
        if win32gui.IsIconic(self.hwnd):
            return WindowState.MINIMIZED
            
        if win32gui.IsZoomed(self.hwnd):
            return WindowState.MAXIMIZED
            
        if self.is_full_screen():
            return WindowState.FULLSCREEN
            
        return WindowState.NORMAL
        
    def tile(self, monitor, position, size):
        """
        Position and size the window on a specific monitor.
        
        Args:
            monitor: Monitor object to position the window on
            position: Tuple (x, y) with relative position (0-1)
            size: Tuple (width, height) with relative size (0-1)
        """
        dimensions = monitor.get_dimensions()
        
        new_x = monitor.monitor_area[0] + int(position[0] * dimensions['width'])
        new_y = monitor.monitor_area[1] + int(position[1] * dimensions['height'])
        new_width = int(size[0] * dimensions['width'])
        new_height = int(size[1] * dimensions['height'])
        
        # Move and resize the window
        win32gui.MoveWindow(self.hwnd, new_x, new_y, new_width, new_height, True)
        self.update_dimensions()
        
    def maximize_within_monitor(self, monitor):
        """
        Maximize the window within a specific monitor.
        
        Args:
            monitor: Monitor object to maximize the window on
        """
        self.tile(monitor, (0, 0), (1, 1))
        
    def center_within_monitor(self, monitor):
        """
        Center the window within a specific monitor.
        
        Args:
            monitor: Monitor object to center the window on
        """
        dimensions = monitor.get_dimensions()
        
        # Calculate center position
        x_position = (dimensions['width'] - self.dimensions['width']) / 2
        y_position = (dimensions['height'] - self.dimensions['height']) / 2
        
        # Convert to proportional coordinates
        x_ratio = x_position / dimensions['width']
        y_ratio = y_position / dimensions['height']
        width_ratio = self.dimensions['width'] / dimensions['width']
        height_ratio = self.dimensions['height'] / dimensions['height']
        
        # Position the window
        self.tile(monitor, (x_ratio, y_ratio), (width_ratio, height_ratio))
        
    def toggle_visibility(self):
        """Toggle the window's visibility."""
        if win32gui.IsWindowVisible(self.hwnd):
            win32gui.ShowWindow(self.hwnd, win32con.SW_HIDE)
        else:
            win32gui.ShowWindow(self.hwnd, win32con.SW_SHOW)
            
    def bring_to_front(self):
        """Bring the window to the front of the z-order."""
        win32gui.SetWindowPos(
            self.hwnd,
            win32con.HWND_TOP,
            self.dimensions['x'],
            self.dimensions['y'],
            self.dimensions['width'],
            self.dimensions['height'],
            win32con.SWP_NOSIZE | win32con.SWP_NOMOVE
        )
        
    def focus(self):
        """Set focus to this window."""
        # Restore if minimized
        if win32gui.IsIconic(self.hwnd):
            win32gui.ShowWindow(self.hwnd, win32con.SW_RESTORE)
        
        # Set foreground window
        win32gui.SetForegroundWindow(self.hwnd)
        
    def is_focused(self):
        """
        Check if the window is currently focused.
        
        Returns:
            bool: True if the window is focused, False otherwise
        """
        return win32gui.GetForegroundWindow() == self.hwnd
        
    def is_visible(self):
        """
        Check if the window is visible.
        
        Returns:
            bool: True if the window is visible, False otherwise
        """
        return win32gui.IsWindowVisible(self.hwnd)
        
    def is_enabled(self):
        """
        Check if the window is enabled.
        
        Returns:
            bool: True if the window is enabled, False otherwise
        """
        return win32gui.IsWindowEnabled(self.hwnd)
        
    def is_minimized(self):
        """
        Check if the window is minimized.
        
        Returns:
            bool: True if the window is minimized, False otherwise
        """
        return win32gui.IsIconic(self.hwnd)
        
    def is_maximized(self):
        """
        Check if the window is maximized.
        
        Returns:
            bool: True if the window is maximized, False otherwise
        """
        return win32gui.IsZoomed(self.hwnd)
        
    def is_full_screen(self):
        """
        Check if the window is in full screen mode.
        
        Returns:
            bool: True if the window is full screen, False otherwise
        """
        # Get screen dimensions
        screen_width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
        screen_height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)
        
        # Get window rect
        rect = win32gui.GetWindowRect(self.hwnd)
        
        # Check if the window covers the entire screen
        return (
            rect[0] == 0 and
            rect[1] == 0 and
            rect[2] == screen_width and
            rect[3] == screen_height
        )
        
    def resize_and_move(self, x, y, width, height):
        """
        Resize and move the window to specified coordinates and dimensions.
        
        Args:
            x: Left position
            y: Top position
            width: Window width
            height: Window height
        """
        win32gui.MoveWindow(self.hwnd, x, y, width, height, True)
        self.update_dimensions()
        
    def __str__(self):
        """Return a string representation of the window."""
        return f"Window(hwnd={self.hwnd}, title='{self.title}', type={self.window_type})"
        
    def __repr__(self):
        """Return a string representation of the window."""
        return self.__str__()


def get_all_windows(visible_only=True, with_titles_only=True, detect_types=False):
    """
    Get all windows in the system.
    
    Args:
        visible_only: Whether to include only visible windows
        with_titles_only: Whether to include only windows with titles
        detect_types: Whether to detect window types
        
    Returns:
        dict: Dictionary mapping window handles to Window objects
    """
    windows = {}
    
    def enum_windows_callback(hwnd, _):
        # Filter windows based on criteria
        if visible_only and not win32gui.IsWindowVisible(hwnd):
            return True
            
        if with_titles_only and not win32gui.GetWindowText(hwnd):
            return True
            
        # Create window object
        windows[hwnd] = Window(hwnd, detect_type=detect_types)
        return True
        
    # Enumerate all windows
    win32gui.EnumWindows(enum_windows_callback, None)
    return windows


def get_windows_by_type(window_type=None, process_name=None, window_class=None):
    """
    Get windows filtered by type, process name, or class.
    
    Args:
        window_type: WindowType to filter by
        process_name: Process name to filter by
        window_class: Window class to filter by
        
    Returns:
        list: List of Window objects matching the criteria
    """
    # Get all windows with types detected
    all_windows = get_all_windows(detect_types=True).values()
    matching_windows = []
    
    for window in all_windows:
        # Filter by window type
        if window_type and window.window_type != window_type:
            continue
            
        # Filter by process name
        if process_name and window.process_info:
            if process_name.lower() not in window.process_info.get('name', '').lower():
                continue
                
        # Filter by window class
        if window_class and window.class_name != window_class:
            continue
            
        # Window passed all filters
        matching_windows.append(window)
        
    return matching_windows
