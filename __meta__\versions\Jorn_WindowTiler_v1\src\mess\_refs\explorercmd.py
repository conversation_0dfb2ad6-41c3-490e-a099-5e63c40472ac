# Import modules
import os
import sys
import re
import time
import ctypes
import urllib.parse
from enum import Enum
# Import pywin32 modules
import win32api
import win32com.client
import win32com.shell.shellcon as shellcon
import win32con
import win32gui
import win32process
import ctypes
import pythoncom

# Using Python, could you create a script that triggers explorer commandhandle '
# {f4e8bc67-9616-4a28-a332-cf27a5ca6736}' (part if @shell32.dll,-37467) on all
# open file explorer windows in Windows 10?

# Where can I find the most comprehensive documentation for Windows' shell32.dll
# other than MSDN?

# def run_explorer_command_on_all_windows(command_id):
#     shell = win32com.client.Dispatch("Shell.Application")
#     windows = shell.Windows()
#     for window in windows:
#         # Check if the window is a File Explorer window
#         if window.Name == "File Explorer":
#             hwnd = int(window.HWND)
#             win32gui.SetForegroundWindow(hwnd)
#             time.sleep(1)
#             # Send the command to the window
#             win32gui.PostMessage(hwnd, win32con.WM_COMMAND, 0x10000000 + command_id, 0)
#             win32gui.SendMessage(hwnd, win32con.WM_COMMAND, 0x10000000 + command_id, 0)
#             # win32gui.SendMessage(hwnd, win32con.WM_COMMAND, 0xF4E8BC67, 0x96164A28)
#             # win32gui.PostMessage(hwnd, win32con.WM_COMMAND, 0x10000000 + command_id, 0)
#             # win32gui.SendMessage(hwnd, win32con.WM_COMMAND, 0xF4E8BC67, 0x96164A28)
#             # win32gui.PostMessage(hwnd, win32con.WM_COMMAND, 0x10000000 + command_id, 0)
#             # win32gui.SendMessage(hwnd, win32con.WM_COMMAND, 0xF4E8BC67, 0x96164A28)
#             win32gui.UpdateWindow(hwnd)

# run_explorer_command_on_all_windows(-37467)

# for x in dir(ctypes.windll.shell32._handle):
#     print(x)
# import ctypes
# import win32com.client

def run_explorer_command_on_all_windows(command_id):
    shell = win32com.client.Dispatch("Shell.Application")
    windows = shell.Windows()
    for window in windows:
        # Check if the window is a File Explorer window
        if window.Name == "File Explorer":
            hwnd = int(window.HWND)
            window.Navigate("shell32.dll,-37467")
            # # Send the command to the window
            # print(dir(ctypes.windll.shell32))
            # ctypes.windll.shell32.ShellExecuteW(hwnd, win32con.WM_COMMAND, 0x10000000 + command_id, 0)
            # Force the window to update
            win32gui.UpdateWindow(hwnd)

run_explorer_command_on_all_windows(37467)
# import ctypes

# def run_explorer_command(command_id):
#     ctypes.windll.shell32.ShellExecuteW(None, "open", "explorer.exe", "shell:::{}".format(command_id), None, 1)

# run_explorer_command("{f4e8bc67-9616-4a28-a332-cf27a5ca6736}")
time.sleep(99999)
print(dir(ctypes.windll.shell32))
print(dir(ctypes.windll.user32))

import ctypes
import win32com.client

def run_explorer_command_on_all_windows(command_id):
    shell = win32com.client.Dispatch("Shell.Application")
    windows = shell.Windows()
    for window in windows:
        # Check if the window is a File Explorer window
        if window.Name == "File Explorer":
            hwnd = int(window.HWND)
            # Send the command to the window
            ctypes.windll.user32.PostMessageW(hwnd, win32con.WM_COMMAND, 0x10000000 + command_id, 0)
            # Force the window to update
            ctypes.windll.user32.UpdateWindow(hwnd)


run_explorer_command_on_all_windows(37467)


# import win32gui
# import win32con

# def enumHandler(hwnd, lParam):
#     if win32gui.IsWindowVisible(hwnd):
#         if 'explorer' in win32gui.GetClassName(hwnd):
#             win32gui.SetForegroundWindow(hwnd)
#             win32gui.SendMessage(hwnd, win32con.WM_COMMAND, 0xF4E8BC67, 0x96164A28)

# win32gui.EnumWindows(enumHandler, None)
