

import sys
import os
import time
import random
import json
import subprocess
import urllib.parse
import argparse
import configparser
# import pywin32
import math
import win32gui
import win32api
from typing import List

# hex_string = "000C1022"
# # 790562
# hex_string = "000A1778"
# # 661368
# hex_string = "000115F8"
# # 71160
# integer_value = int(hex_string, 16)
# print(integer_value)
# time.sleep(9999)


# TILING 1 (WORKS): BASICLY FILLS THE SCREEN WITH WINDOWS IN GRID (ROWS/COLUMNS)
def snap_windows_to_fill_screen(hwnd_list):
    screen_width = win32api.GetSystemMetrics(0)
    screen_height = win32api.GetSystemMetrics(1)

    num_windows = len(hwnd_list)
    num_columns = math.ceil(math.sqrt(num_windows))
    num_rows = math.ceil(num_windows / num_columns)

    window_width = screen_width // num_columns
    window_height = screen_height // num_rows

    index = 0
    for row in range(num_rows):
        for column in range(num_columns):
            if index < num_windows:
                hwnd = hwnd_list[index]
                x = column * window_width
                y = row * window_height
                win32gui.MoveWindow(hwnd, x, y, window_width, window_height, True)
                index += 1
            else:
                break


# TILING 2 (WORKS): FILLS THE SCREEN DYNAMICALLY (EACH EDGE OF THE WINDOW IS INDEPENDENT)
def get_window_rectangles(hwnd_list: List[int]) -> List[List[int]]:
    return [win32gui.GetWindowRect(hwnd) for hwnd in hwnd_list]

def has_intersection(rect1, rect2):
    return not (rect1[2] < rect2[0] or rect1[0] > rect2[2] or rect1[1] > rect2[3] or rect1[3] < rect2[1])

def grow_windows_incrementally(hwnd_list: List[int]):
    window_rectangles = get_window_rectangles(hwnd_list)
    screen_width = win32api.GetSystemMetrics(0)
    screen_height = win32api.GetSystemMetrics(1)
    def update_window(index):
        hwnd = hwnd_list[index]
        rect = window_rectangles[index]

        directions = [
            (-1, 0),  # Left
            (1, 0),   # Right
            (0, -1),  # Up
            (0, 1)    # Down
        ]

        updated_rect = list(rect[:])

        for direction in directions:
            current_rect = updated_rect[:]

            while True:
                new_rect = [
                    current_rect[0] + direction[0],
                    current_rect[1] + direction[1],
                    current_rect[2] + direction[0],
                    current_rect[3] + direction[1]
                ]

                # Check if the new rectangle is within the screen bounds
                if new_rect[0] < 0 or new_rect[1] < 0 or new_rect[2] > screen_width or new_rect[3] > screen_height:
                    break

                # Check if the new rectangle intersects with any other window
                intersects = False
                intersection_index = -1
                for i, other_rect in enumerate(window_rectangles):
                    if i != index and has_intersection(new_rect, other_rect):
                        intersects = True
                        intersection_index = i
                        break

                # Check if the new rectangle reaches the screen edge
                reaches_edge = (
                    new_rect[0] == 0 or
                    new_rect[1] == 0 or
                    new_rect[2] == screen_width or
                    new_rect[3] == screen_height
                )

                if intersects:
                    intersecting_rect = window_rectangles[intersection_index]

                    if direction == (-1, 0):  # Left
                        new_rect[0] = intersecting_rect[2]
                    elif direction == (1, 0):  # Right
                        new_rect[2] = intersecting_rect[0]
                    elif direction == (0, -1):  # Up
                        new_rect[1] = intersecting_rect[3]
                    elif direction == (0, 1):  # Down
                        new_rect[3] = intersecting_rect[1]

                    break

                if reaches_edge:
                    break
                else:
                    current_rect = new_rect

            if direction == (-1, 0):  # Left
                updated_rect[0] = current_rect[0]
            elif direction == (1, 0):  # Right
                updated_rect[2] = current_rect[2]
            elif direction == (0, -1):  # Up
                updated_rect[1] = current_rect[1]
            elif direction == (0, 1):  # Down
                updated_rect[3] = current_rect[3]

        window_rectangles[index] = updated_rect
        win32gui.MoveWindow(hwnd, updated_rect[0], updated_rect[1], updated_rect[2] - updated_rect[0], updated_rect[3] - updated_rect[1], True)

    for index in range(len(hwnd_list)):
        update_window(index)





# TILING 2 (WORKS): FILLS THE SCREEN DYNAMICALLY (EACH EDGE OF THE WINDOW IS INDEPENDENT)
def get_window_rectangles(hwnd_list: List[int]) -> List[List[int]]:
    return [win32gui.GetWindowRect(hwnd) for hwnd in hwnd_list]

def has_intersection(rect1, rect2):
    return not (rect1[2] < rect2[0] or rect1[0] > rect2[2] or rect1[1] > rect2[3] or rect1[3] < rect2[1])

def grow_windows_incrementally(hwnd_list: List[int]):
    window_rectangles = get_window_rectangles(hwnd_list)
    screen_width = win32api.GetSystemMetrics(0)
    screen_height = win32api.GetSystemMetrics(1)

    def update_window(index):
        hwnd = hwnd_list[index]
        rect = window_rectangles[index]

        directions = [
            (-1, 0),  # Left
            (1, 0),   # Right
            (0, -1),  # Up
            (0, 1)    # Down
        ]

        updated_rect = list(rect[:])

        for direction in directions:
            current_rect = updated_rect[:]

            while True:
                new_rect = [
                    current_rect[0] + direction[0],
                    current_rect[1] + direction[1],
                    current_rect[2] + direction[0],
                    current_rect[3] + direction[1]
                ]

                # Check if the new rectangle is within the screen bounds
                if new_rect[0] < 0 or new_rect[1] < 0 or new_rect[2] > screen_width or new_rect[3] > screen_height:
                    break

                # Check if the new rectangle intersects with any other window
                intersects = False
                intersection_index = -1
                for i, other_rect in enumerate(window_rectangles):
                    if i != index and has_intersection(new_rect, other_rect):
                        intersects = True
                        intersection_index = i
                        break

                # Check if the new rectangle reaches the screen edge
                reaches_edge = (
                    new_rect[0] == 0 or
                    new_rect[1] == 0 or
                    new_rect[2] == screen_width or
                    new_rect[3] == screen_height
                )

                if intersects:
                    intersecting_rect = window_rectangles[intersection_index]

                    if direction == (-1, 0):  # Left
                        new_rect[0] = (intersecting_rect[2] + rect[0]) // 2
                    elif direction == (1, 0):  # Right
                        new_rect[2] = (intersecting_rect[0] + rect[2]) // 2
                    elif direction == (0, -1):  # Up
                        new_rect[1] = (intersecting_rect[3] + rect[1]) // 2
                    elif direction == (0, 1):  # Down
                        new_rect[3] = (intersecting_rect[1] + rect[3]) // 2

                    break

                if reaches_edge:
                    break
                else:
                    current_rect = new_rect
            # time.sleep(1)
            if direction == (-1, 0):  # Left
                updated_rect[0] = current_rect[0]
            elif direction == (1, 0):  # Right
                updated_rect[2] = current_rect[2]
            elif direction == (0, -1):  # Up
                updated_rect[1] = current_rect[1]
            elif direction == (0, 1):  # Down
                updated_rect[3] = current_rect[3]

        window_rectangles[index] = updated_rect
        win32gui.MoveWindow(hwnd, updated_rect[0], updated_rect[1], updated_rect[2] - updated_rect[0], updated_rect[3] - updated_rect[1], True)

    for index in range(len(hwnd_list)):
        update_window(index)

grow_windows_incrementally([790562, 661368, 71160])


# # TILING 3 (NOTWORKING):
# def get_window_rectangles(hwnd_list: List[int]) -> List[List[int]]:
#     return [win32gui.GetWindowRect(hwnd) for hwnd in hwnd_list]

# def has_intersection(rect1, rect2):
#     return (
#         rect1[0] < rect2[2] and
#         rect1[2] > rect2[0] and
#         rect1[1] < rect2[3] and
#         rect1[3] > rect2[1]
#     )
# def grow_windows_incrementally(hwnd_list: List[int]):
#     window_rectangles = get_window_rectangles(hwnd_list)
#     screen_width = win32api.GetSystemMetrics(0)
#     screen_height = win32api.GetSystemMetrics(1)

#     def grow_windows_simultaneously():
#         directions = [
#             (-1, 0),  # Left
#             (1, 0),   # Right
#             (0, -1),  # Up
#             (0, 1)    # Down
#         ]

#         while True:
#             has_updated = False

#             for index in range(len(hwnd_list)):
#                 hwnd = hwnd_list[index]
#                 rect = window_rectangles[index]

#                 for direction in directions:
#                     new_rect = [
#                         rect[0] + direction[0],
#                         rect[1] + direction[1],
#                         rect[2] + direction[0],
#                         rect[3] + direction[1]
#                     ]

#                     # Check if the new rectangle is within the screen bounds
#                     if new_rect[0] < 0 or new_rect[1] < 0 or new_rect[2] > screen_width or new_rect[3] > screen_height:
#                         continue

#                     # Check if the new rectangle intersects with any other window
#                     intersects = False
#                     for i, other_rect in enumerate(window_rectangles):
#                         if i != index and has_intersection(new_rect, other_rect):
#                             intersects = True
#                             break

#                     if not intersects:
#                         window_rectangles[index] = new_rect
#                         has_updated = True

#             if not has_updated:
#                 break

#         for index in range(len(hwnd_list)):
#             hwnd = hwnd_list[index]
#             rect = window_rectangles[index]
#             win32gui.MoveWindow(hwnd, rect[0], rect[1], rect[2] - rect[0], rect[3] - rect[1], True)

#     grow_windows_simultaneously()



# # TILING 4 (WIP):
# def get_window_rectangles(hwnd_list: List[int]) -> List[List[int]]:
#     return [win32gui.GetWindowRect(hwnd) for hwnd in hwnd_list]

# def has_intersection(rect1, rect2):
#     return (
#         rect1[0] < rect2[2] and
#         rect1[2] > rect2[0] and
#         rect1[1] < rect2[3] and
#         rect1[3] > rect2[1]
#     )
# def grow_windows_incrementally(hwnd_list: List[int]):
#     # window_rectangles = get_window_rectangles(hwnd_list)
#     window_rectangles = [list(win32gui.GetWindowRect(hwnd)) for hwnd in hwnd_list]

#     screen_width = win32api.GetSystemMetrics(0)
#     screen_height = win32api.GetSystemMetrics(1)

#     def grow_windows_uniformly():
#         horizontal_gaps = []
#         vertical_gaps = []

#         for index in range(len(hwnd_list)):
#             rect = window_rectangles[index]
#             horizontal_gaps.append((rect[0], index, 'left'))
#             horizontal_gaps.append((rect[2], index, 'right'))
#             vertical_gaps.append((rect[1], index, 'top'))
#             vertical_gaps.append((rect[3], index, 'bottom'))

#         horizontal_gaps.sort()
#         vertical_gaps.sort()

#         def fill_gaps(gaps, max_coordinate):
#             gap_starts = [0] + [g[0] for g in gaps[1:]]
#             gap_ends = [g[0] for g in list(gaps[:-1])] + [max_coordinate]

#             for i, gap_start in enumerate(gap_starts):
#                 gap_end = gap_ends[i]
#                 gap_size = gap_end - gap_start

#                 if gap_size > 0:
#                     gap_index = gaps[i][1]
#                     gap_edge = gaps[i][2]
#                     rect = window_rectangles[gap_index]

#                     if gap_edge == 'left':
#                         rect[0] += gap_size // 2
#                         rect[2] += gap_size // 2
#                     elif gap_edge == 'right':
#                         rect[0] -= gap_size // 2
#                         rect[2] -= gap_size // 2
#                     elif gap_edge == 'top':
#                         rect[1] += gap_size // 2
#                         rect[3] += gap_size // 2
#                     elif gap_edge == 'bottom':
#                         rect[1] -= gap_size // 2
#                         rect[3] -= gap_size // 2

#                     window_rectangles[gap_index] = rect

#         fill_gaps(horizontal_gaps, screen_width)
#         fill_gaps(vertical_gaps, screen_height)

#         for index in range(len(hwnd_list)):
#             time.sleep(1)
#             hwnd = hwnd_list[index]
#             rect = window_rectangles[index]
#             win32gui.MoveWindow(hwnd, rect[0], rect[1], rect[2] - rect[0], rect[3] - rect[1], True)

#     grow_windows_uniformly()

# grow_windows_incrementally([790562, 661368, 71160])


# snap_windows_to_fill_screen([790562,661368,71160])
# grow_windows_incrementally([790562,661368,71160])
