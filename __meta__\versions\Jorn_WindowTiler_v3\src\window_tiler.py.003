#!/usr/bin/env python3
"""
Simplified Window Tiler (with minimized windows support)

- Enumerates *all* windows (including minimized ones).
- Groups them by process name OR by a simple window "type".
- Lets you pick which group you want to tile.
- Tiles in a grid on your selected monitor (restoring minimized windows).

Requirements:
    pip install pywin32
"""

import win32api
import win32gui
import win32con
import win32process
import ctypes
import os
import sys

# -------------------------------------------------------------------------
# Minimal Enums / Classes
# -------------------------------------------------------------------------
from enum import Enum, auto

class WindowType(Enum):
    BROWSER = auto()
    TERMINAL = auto()
    EXPLORER = auto()
    EDITOR = auto()
    IDE = auto()
    NORMAL = auto()
    UNKNOWN = auto()

class Monitor:
    """Represents a physical display monitor."""
    def __init__(self, handle, info):
        self.handle = handle
        self.is_primary = bool(info["Flags"] == 1)
        self.device = info["Device"]

        mon_rect = info["Monitor"]  # (left, top, right, bottom)
        work_rect = info["Work"]    # (left, top, right, bottom)
        self.monitor_area = mon_rect
        self.work_area = work_rect

    def get_dimensions(self):
        left, top, right, bottom = self.monitor_area
        return {"width": right - left, "height": bottom - top}

class Window:
    """Represents a single window with minimal attributes."""
    def __init__(self, hwnd):
        self.hwnd = hwnd
        self.title = win32gui.GetWindowText(hwnd)
        self.class_name = win32gui.GetClassName(hwnd)
        self.process_name = None
        self.window_type = WindowType.UNKNOWN
        self._update_process_info()
        self._classify()

    def _update_process_info(self):
        """Get the process name from the window handle."""
        try:
            _, pid = win32process.GetWindowThreadProcessId(self.hwnd)
            proc_access = win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ
            handle = ctypes.windll.kernel32.OpenProcess(proc_access, False, pid)
            if handle:
                exe_path = win32process.GetModuleFileNameEx(handle, 0)
                self.process_name = os.path.basename(exe_path).lower()
                ctypes.windll.kernel32.CloseHandle(handle)
        except:
            pass

    def _classify(self):
        """A very rough classification of the window based on process name."""
        if not self.process_name:
            self.window_type = WindowType.UNKNOWN
            return

        if any(b in self.process_name for b in ["chrome.exe", "msedge.exe", "firefox.exe", "iexplore.exe"]):
            self.window_type = WindowType.BROWSER
        elif any(t in self.process_name for t in ["cmd.exe", "powershell.exe", "windowsterminal.exe"]):
            self.window_type = WindowType.TERMINAL
        elif "explorer.exe" in self.process_name or self.class_name == "CabinetWClass":
            self.window_type = WindowType.EXPLORER
        elif any(e in self.process_name for e in ["notepad.exe", "code.exe", "sublime_text.exe"]):
            self.window_type = WindowType.EDITOR
        elif any(i in self.process_name for i in ["devenv.exe", "pycharm", "idea64"]):
            self.window_type = WindowType.IDE
        else:
            self.window_type = WindowType.NORMAL

    def move_and_resize(self, x, y, width, height):
        """
        Restore (if minimized) and position this window.

        Note: We call ShowWindow(..., SW_RESTORE) to ensure minimized windows
        become visible prior to moving.
        """
        win32gui.ShowWindow(self.hwnd, win32con.SW_RESTORE)
        win32gui.MoveWindow(self.hwnd, x, y, width, height, True)

    def __repr__(self):
        return f"<Window hwnd={self.hwnd} title='{self.title}' proc='{self.process_name}' type={self.window_type.name}>"

# -------------------------------------------------------------------------
# 1. Collecting All Windows & Grouping
# -------------------------------------------------------------------------
def get_all_windows():
    """
    Returns a list of Window objects, including minimized and invisible ones.
    (We still skip certain known system placeholders and empty titles.)
    """
    results = []

    # Skip certain system classes
    system_classes = {
        'Default IME', 'MSCTFIME UI', 'CiceroUIWndFrame',
        'GDI+ Window', 'MediaContextNotificationWindow',
        'SystemResourceNotifyWindow', '#32770',
        'DesktopWindowXamlSource', 'DDE Server Window',
        'Windows.UI.Core.CoreWindow'
    }
    # Skip certain system "titles"
    skip_titles = {"Task Switching", "Program Manager", "Windows Input Experience"}

    def enum_callback(hwnd, _):
        # For minimal filtering, we only skip if there's *no title* or if it's an obvious system class/title.
        title = win32gui.GetWindowText(hwnd)
        cls = win32gui.GetClassName(hwnd)

        if not title or title in skip_titles:
            return True
        if cls in system_classes:
            return True

        # Capture it, even if it's minimized or not visible
        results.append(Window(hwnd))
        return True

    win32gui.EnumWindows(enum_callback, None)
    return results

def group_by_process_name(windows):
    """
    Group windows by their process name.
    Returns a dict: {process_name: [Window, Window, ...]}
    """
    groups = {}
    for w in windows:
        pname = w.process_name or "unknown"
        groups.setdefault(pname, []).append(w)
    return groups

def group_by_window_type(windows):
    """
    Group windows by their WindowType (BROWSER, TERMINAL, etc.).
    Returns a dict: {WindowType: [Window, Window, ...]}
    """
    groups = {}
    for w in windows:
        wtype = w.window_type
        groups.setdefault(wtype, []).append(w)
    return groups

# -------------------------------------------------------------------------
# 2. Monitor Handling
# -------------------------------------------------------------------------
class WrappedMonitor:
    """Simplified wrapper for user selection."""
    def __init__(self, monitor_obj, index):
        self.monitor_obj = monitor_obj
        self.index = index

    def __str__(self):
        dims = self.monitor_obj.get_dimensions()
        primary_txt = " [PRIMARY]" if self.monitor_obj.is_primary else ""
        return f"{self.index}) {self.monitor_obj.device} - {dims['width']}x{dims['height']}{primary_txt}"

def get_all_monitors():
    """Return a list of Monitor objects."""
    monitors = []
    for (handle, _, info) in win32api.EnumDisplayMonitors(None, None):
        mon_info = win32api.GetMonitorInfo(handle)
        monitors.append(Monitor(handle, mon_info))
    return monitors

def choose_monitor():
    """Prompt user to choose a monitor (or pick primary by default)."""
    monitor_objs = get_all_monitors()
    if not monitor_objs:
        print("No monitors found!")
        sys.exit(1)

    wrapped = []
    for i, m in enumerate(monitor_objs):
        wrapped.append(WrappedMonitor(m, i))

    print("\nMonitors Detected:")
    for w in wrapped:
        print(w)

    choice = input("Select a monitor index [blank=choose primary]: ").strip()
    if not choice:
        # Attempt to pick the primary
        for m in monitor_objs:
            if m.is_primary:
                return m
        return monitor_objs[0]  # fallback

    try:
        idx = int(choice)
        if 0 <= idx < len(monitor_objs):
            return monitor_objs[idx]
        else:
            print("Invalid monitor index, defaulting to first.")
            return monitor_objs[0]
    except:
        print("Invalid input, defaulting to first monitor.")
        return monitor_objs[0]

# -------------------------------------------------------------------------
# 3. Tiling
# -------------------------------------------------------------------------
def tile_in_grid(monitor, windows, rows=2, cols=2):
    """
    Simple grid tiling on the chosen monitor.
    Minimizes advanced checks. Just brute-force each cell.
    """
    if not windows:
        print("No windows to tile.")
        return

    left, top, right, bottom = monitor.monitor_area
    total_width = right - left
    total_height = bottom - top

    n = min(len(windows), rows * cols)

    for i, w in enumerate(windows[:n]):
        row = i // cols
        col = i % cols

        x = left + int(col * (total_width / cols))
        y = top + int(row * (total_height / rows))
        wth = int(total_width / cols)
        hth = int(total_height / rows)

        w.move_and_resize(x, y, wth, hth)

    print(f"Tiled {n} windows in a {rows}x{cols} grid on {monitor.device}.")

# -------------------------------------------------------------------------
# 4. Main CLI
# -------------------------------------------------------------------------
def main():
    print("Gathering all windows (including minimized ones)...")
    windows = get_all_windows()
    if not windows:
        print("No windows found!")
        return

    print("\nChoose grouping style:")
    print("1) By process name (e.g. 'chrome.exe')")
    print("2) By window type (BROWSER, TERMINAL, EDITOR, etc.)")
    choice = input("Enter [1/2]: ").strip()

    if choice == "1":
        groups = group_by_process_name(windows)
    else:
        groups = group_by_window_type(windows)

    # Show groups
    print("\nGroups Detected:")
    group_keys = sorted(groups.keys(), key=lambda k: str(k))
    for idx, key in enumerate(group_keys):
        print(f"{idx}) {key} -> {len(groups[key])} windows")

    if not group_keys:
        print("No groups found!")
        return

    chosen = input("\nWhich group do you want to tile? Enter index: ").strip()
    try:
        chosen_idx = int(chosen)
        group_key = group_keys[chosen_idx]
    except:
        print("Invalid choice, quitting.")
        return

    r = input("Number of rows [default=2]: ").strip()
    c = input("Number of columns [default=2]: ").strip()
    rows = int(r) if r.isdigit() else 2
    cols = int(c) if c.isdigit() else 2

    monitor = choose_monitor()
    tile_in_grid(monitor, groups[group_key], rows, cols)

    print("\nDone.")

if __name__ == "__main__":
    main()
