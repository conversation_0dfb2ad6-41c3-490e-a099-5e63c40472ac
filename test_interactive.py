#!/usr/bin/env python3
"""
Test script to verify the interactive functionality of Window Tiler.
This script demonstrates the persistent interactive session capabilities.
"""

import sys
import os

# Add the src directory to the path so we can import window_tiler
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from window_tiler import WindowTilerApp, Config

def test_interactive_mode():
    """Test the interactive mode functionality."""
    print("Testing Window Tiler Interactive Mode")
    print("=" * 50)
    
    # Create a custom config for testing
    config = Config()
    
    # Create the app
    app = WindowTilerApp(config)
    
    print("✅ WindowTilerApp created successfully")
    print("✅ Interactive mode components initialized")
    print("✅ All required methods are available:")
    
    # Check that all required methods exist
    required_methods = [
        'run', '_handle_tile_windows', '_handle_list_windows', 
        '_handle_list_monitors', '_handle_show_help', '_handle_exit', 'exit'
    ]
    
    for method in required_methods:
        if hasattr(app, method):
            print(f"   ✅ {method}")
        else:
            print(f"   ❌ {method} - MISSING!")
            return False
    
    print("\n✅ All interactive functionality is properly implemented!")
    print("\nTo test the full interactive experience, run:")
    print("   python src/window_tiler.py")
    print("\nOr for legacy one-time mode:")
    print("   python src/window_tiler.py --once")
    
    return True

def test_ui_components():
    """Test the UI components for interactive mode."""
    print("\nTesting UI Components")
    print("=" * 30)
    
    from window_tiler import UserInterface
    
    ui = UserInterface()
    
    # Check that all required UI methods exist
    required_ui_methods = [
        'show_main_menu', 'show_help', 'confirm_continue',
        'list_all_windows', 'list_monitors'
    ]
    
    for method in required_ui_methods:
        if hasattr(ui, method):
            print(f"   ✅ {method}")
        else:
            print(f"   ❌ {method} - MISSING!")
            return False
    
    print("✅ All UI components are properly implemented!")
    return True

if __name__ == "__main__":
    print("Window Tiler Interactive Mode Test")
    print("=" * 60)
    
    success = True
    
    try:
        success &= test_interactive_mode()
        success &= test_ui_components()
        
        if success:
            print("\n🎉 ALL TESTS PASSED!")
            print("\nThe Window Tiler now supports persistent interactive sessions!")
            print("Key features implemented:")
            print("• Continuous loop until explicit exit")
            print("• Main menu with multiple options")
            print("• Error handling and recovery")
            print("• Graceful Ctrl+C handling")
            print("• Help system")
            print("• Window and monitor listing")
            print("• Confirmation prompts")
            
        else:
            print("\n❌ SOME TESTS FAILED!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ TEST ERROR: {e}")
        sys.exit(1)
