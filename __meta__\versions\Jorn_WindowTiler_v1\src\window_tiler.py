class WindowTiler:
    def __init__(self, monitors, windows):
        self.monitors = monitors
        self.windows = windows

    def tile_windows(self, monitor, windows, rows, columns, column_ratios=None, row_ratios=None):
        if rows <= 0 or columns <= 0:
            raise ValueError("Rows and columns must be greater than 0")

        monitor_dimensions = monitor.get_dimensions()
        default_width_ratio = 1 / columns
        default_height_ratio = 1 / rows

        for i, window in enumerate(windows):
            col = i % columns
            row = i // columns

            x_ratio = sum(column_ratios[:col]) if column_ratios else col * default_width_ratio
            y_ratio = sum(row_ratios[:row]) if row_ratios else row * default_height_ratio
            width_ratio = column_ratios[col] if column_ratios else default_width_ratio
            height_ratio = row_ratios[row] if row_ratios else default_height_ratio

            window.tile(monitor, (x_ratio, y_ratio), (width_ratio, height_ratio))
