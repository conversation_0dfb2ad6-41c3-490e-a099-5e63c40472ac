#!/usr/bin/env python3
"""
Window Tiler - Automatically arrange windows in grids

Groups windows by process or type, then tiles them in customizable grids.
Supports minimized windows and multiple monitors.

Requirements: pip install pywin32 psutil rich

Customize window types by modifying WindowTilerConfig class below.
"""

import os
import sys
import math
import argparse
import psutil
import win32api
import win32gui
import win32con
import win32process
from enum import Enum, auto
from rich.console import Console
from rich.prompt import Prompt, Confirm

class Config:
    def __init__(self):
        self.types = {
            'chrome.exe': 'Browser', 'firefox.exe': 'Browser', 'msedge.exe': 'Browser',
            'cmd.exe': 'Terminal', 'powershell.exe': 'Terminal', 'windowsterminal.exe': 'Terminal',
            'code.exe': 'Editor', 'notepad.exe': 'Editor', 'sublime_text.exe': 'Editor',
            'devenv.exe': 'IDE', 'pycharm64.exe': 'IDE', 'idea64.exe': 'IDE',
            'explorer.exe': 'Explorer'
        }

        self.skip = {'dwm.exe', 'winlogon.exe', 'csrss.exe', 'svchost.exe', 'lsass.exe'}

    def get_type(self, process_name):
        return self.types.get(process_name.lower(), 'Other')

    def should_skip(self, process_name):
        return process_name.lower() in self.skip

# ---------------------------------------------------------
# Enumerations & Basic Classes
# ---------------------------------------------------------
class WindowType(Enum):
    BROWSER = auto()
    TERMINAL = auto()
    EXPLORER = auto()
    EDITOR = auto()
    IDE = auto()
    NORMAL = auto()
    UNKNOWN = auto()

class Monitor:
    """Represents a physical display monitor."""

    def __init__(self, handle, info):
        self.handle = handle
        self.is_primary = bool(info["Flags"] == 1)
        self.device = info["Device"]
        # Monitor area (left, top, right, bottom)
        mon_rect = info["Monitor"]
        work_rect = info["Work"]
        self.monitor_area = mon_rect
        self.work_area = work_rect

    def get_dimensions(self):
        left, top, right, bottom = self.monitor_area
        return {"width": right - left, "height": bottom - top}

    def get_work_dimensions(self):
        """Get work area dimensions (excludes taskbar and other system UI)."""
        left, top, right, bottom = self.work_area
        return {"width": right - left, "height": bottom - top}

# ---------------------------------------------------------
# Existing Window, Monitor Classes
# ---------------------------------------------------------
class Window:
    def __init__(self, hwnd, process_name, config=None):
        self.hwnd = hwnd
        self.title = win32gui.GetWindowText(hwnd)
        self.process_name = process_name
        self.config = config or Config()
        self.type = self.config.get_type(process_name)

    def move_and_resize(self, x, y, width, height, restore_minimized=True, bring_to_front=False):
        """Position and optionally restore the window."""
        was_minimized = win32gui.IsIconic(self.hwnd)

        if restore_minimized and was_minimized:
            # SW_SHOWNORMAL is more reliable than SW_RESTORE for typical apps
            win32gui.ShowWindow(self.hwnd, win32con.SW_SHOWNORMAL)

        # Move it
        win32gui.MoveWindow(self.hwnd, x, y, width, height, True)

        # Bring to front if requested (apply to all windows when enabled, not just restored ones)
        if bring_to_front:
            self._force_window_to_front(x, y, width, height)
        else:
            # Standard positioning without stealing focus
            win32gui.SetWindowPos(
                self.hwnd,
                win32con.HWND_TOP,
                x, y, width, height,
                win32con.SWP_NOSENDCHANGING | win32con.SWP_SHOWWINDOW
            )

    def _force_window_to_front(self, x, y, width, height):
        """Use multiple methods to force window to front of z-order."""
        try:
            # Method 1: Get current foreground window and thread
            current_fg = win32gui.GetForegroundWindow()
            current_thread = win32process.GetWindowThreadProcessId(current_fg)[0]
            target_thread = win32process.GetWindowThreadProcessId(self.hwnd)[0]

            # Method 2: Attach to foreground thread to bypass restrictions
            if current_thread != target_thread:
                win32process.AttachThreadInput(target_thread, current_thread, True)

            # Method 3: Temporarily make topmost, then remove topmost
            win32gui.SetWindowPos(
                self.hwnd,
                win32con.HWND_TOPMOST,
                x, y, width, height,
                win32con.SWP_SHOWWINDOW
            )

            # Method 4: Set as foreground window
            win32gui.SetForegroundWindow(self.hwnd)

            # Method 5: Remove topmost status but keep it at the top
            win32gui.SetWindowPos(
                self.hwnd,
                win32con.HWND_NOTOPMOST,
                x, y, width, height,
                win32con.SWP_SHOWWINDOW
            )

            # Method 6: Detach thread input
            if current_thread != target_thread:
                win32process.AttachThreadInput(target_thread, current_thread, False)

        except Exception:
            # Fallback: just use topmost toggle
            win32gui.SetWindowPos(
                self.hwnd,
                win32con.HWND_TOPMOST,
                x, y, width, height,
                win32con.SWP_SHOWWINDOW
            )
            win32gui.SetWindowPos(
                self.hwnd,
                win32con.HWND_NOTOPMOST,
                x, y, width, height,
                win32con.SWP_SHOWWINDOW
            )

    def __repr__(self):
        return f"<Window hwnd={self.hwnd} title='{self.title}' type={self.type}>"

class WindowManager:
    def __init__(self, config=None):
        self.windows = []
        self.config = config or Config()

    def detect_windows(self):
        self.windows = []

        def enum_callback(hwnd, _):
            if not win32gui.IsWindowVisible(hwnd):
                return True

            title = win32gui.GetWindowText(hwnd)
            if not title:
                return True

            try:
                _, pid = win32process.GetWindowThreadProcessId(hwnd)
                proc = psutil.Process(pid)
                process_name = os.path.basename(proc.exe())

                if self.config.should_skip(process_name):
                    return True

                self.windows.append(Window(hwnd, process_name, self.config))
            except:
                pass

            return True

        win32gui.EnumWindows(enum_callback, None)

    def get_windows_by_process(self):
        groups = {}
        for w in self.windows:
            groups.setdefault(w.process_name, []).append(w)
        return groups

    def get_windows_by_type(self):
        groups = {}
        for w in self.windows:
            groups.setdefault(w.type, []).append(w)
        return groups

    def get_all_windows_as_group(self):
        """Return all windows as a single group for 'tile all' functionality."""
        return {"All Windows": self.windows}



# ---------------------------------------------------------
# 2) MonitorManager
# ---------------------------------------------------------
class MonitorManager:
    """Responsible for discovering monitors and retrieving user-chosen monitors."""
    def __init__(self):
        self.monitors = []

    def detect_monitors(self):
        """Populate self.monitors with discovered Monitor objects."""
        self.monitors = []
        for (handle, _, info) in win32api.EnumDisplayMonitors(None, None):
            mon_info = win32api.GetMonitorInfo(handle)
            self.monitors.append(Monitor(handle, mon_info))

    def get_primary_monitor(self):
        """Return the primary monitor, or None if none found."""
        for m in self.monitors:
            if m.is_primary:
                return m
        return self.monitors[0] if self.monitors else None

    def get_monitor_by_index(self, index):
        """Return monitor by list index or None if out of range."""
        if 0 <= index < len(self.monitors):
            return self.monitors[index]
        return None

# ---------------------------------------------------------
# 3) LayoutManager
# ---------------------------------------------------------
class LayoutManager:
    """
    Responsible for applying different layout strategies (grid, custom, etc.).
    """
    def __init__(self):
        self.console = Console()

    def apply_grid_layout(self, windows, monitor, rows=2, cols=2, restore_minimized=True, bring_to_front=False, use_work_area=True):
        """Simple grid layout on the chosen monitor."""
        if not windows:
            self.console.print("No windows to tile.")
            return

        # Use work area (excludes taskbar) by default, or full monitor area if requested
        if use_work_area:
            left, top, right, bottom = monitor.work_area
            area_type = "work area (taskbar-aware)"
        else:
            left, top, right, bottom = monitor.monitor_area
            area_type = "full monitor area"

        total_width = right - left
        total_height = bottom - top

        n = min(len(windows), rows * cols)

        for i, w in enumerate(windows[:n]):
            row = i // cols
            col = i % cols

            x = left + int(col * (total_width / cols))
            y = top + int(row * (total_height / rows))
            wth = int(total_width / cols)
            hth = int(total_height / rows)

            w.move_and_resize(x, y, wth, hth, restore_minimized=restore_minimized, bring_to_front=bring_to_front)

        self.console.print(f"Tiled {n} windows in a {rows}x{cols} grid on {monitor.device} using {area_type}.")

    def apply_custom_layout(self, windows, monitor, layout_config):
        """Example placeholder for custom layouts."""
        pass

    def create_layout_preset(self, name, config):
        """Register a named preset for future usage."""
        pass

# ---------------------------------------------------------
# 4) UserInterface
# ---------------------------------------------------------
class UserInterface:
    """Handles all user-facing input/output prompts using rich for better formatting."""

    def __init__(self):
        self.console = Console()

    def show_monitor_selection(self, monitors):
        """Display available monitors in a simple format."""
        self.console.print("\nMonitors Detected:")
        for i, mon in enumerate(monitors):
            dims = mon.get_dimensions()
            work_dims = mon.get_work_dimensions()
            primary_txt = " [PRIMARY]" if mon.is_primary else ""
            self.console.print(f"{i + 1}) {mon.device} - {dims['width']}x{dims['height']} (work: {work_dims['width']}x{work_dims['height']}){primary_txt}")

    def prompt_monitor_index(self):
        return Prompt.ask("Select a monitor index", default="", show_default=False).strip()

    def show_grouped_windows(self, grouped):
        """Display grouped windows sorted by window count (descending)."""
        # Sort groups by window count (descending), then by name
        group_keys = sorted(grouped.keys(), key=lambda k: (-len(grouped[k]), str(k)))

        self.console.print("\nGroups Detected:")
        for idx, key in enumerate(group_keys):
            label = str(key)
            if isinstance(key, WindowType):
                label = key.name

            count = len(grouped[key])
            self.console.print(f"{idx + 1}) {label} -> {count} windows")

        return group_keys

    def get_user_choice_index(self):
        return Prompt.ask("\nWhich group do you want to tile? Enter index").strip()

    def get_layout_configuration(self, num_windows):
        """Prompt for rows, columns, and whether to restore minimized."""
        # Calculate dynamic defaults based on number of windows
        default_rows, default_cols = self._calculate_optimal_grid(num_windows)

        # Get rows first
        rows = Prompt.ask(f"Number of rows", default=str(default_rows))
        rows = int(rows) if rows.isdigit() else default_rows

        # Recalculate column default based on user's row choice
        adjusted_cols = math.ceil(num_windows / rows)

        # Get columns with the adjusted default
        cols = Prompt.ask(f"Number of columns", default=str(adjusted_cols))
        cols = int(cols) if cols.isdigit() else adjusted_cols

        restore_minimized = Confirm.ask("Restore minimized windows?", default=True)

        # Only ask about bringing to front if restore is enabled
        bring_to_front = False
        if restore_minimized:
            bring_to_front = Confirm.ask("Force windows to foreground (aggressive activation)?", default=False)

        # Ask about taskbar-aware tiling
        use_work_area = Confirm.ask("Use taskbar-aware tiling (recommended)?", default=True)

        return rows, cols, restore_minimized, bring_to_front, use_work_area

    def _calculate_optimal_grid(self, num_windows):
        """Calculate optimal rows and columns for the given number of windows."""
        if num_windows <= 0:
            return 2, 2

        # For small numbers of windows, use simple layouts
        if num_windows == 1:
            return 1, 1
        elif num_windows == 2:
            return 1, 2
        elif num_windows == 3:
            return 1, 3
        elif num_windows == 4:
            return 2, 2
        elif num_windows <= 6:
            return 2, 3
        elif num_windows <= 8:
            return 2, 4
        elif num_windows == 9:
            return 3, 3
        elif num_windows <= 12:
            return 3, 4
        elif num_windows <= 15:
            return 3, 5
        elif num_windows == 16:
            return 4, 4
        else:
            # For larger numbers, try to create a roughly square grid
            # that can accommodate all windows
            sqrt_windows = math.sqrt(num_windows)
            rows = int(sqrt_windows)
            cols = math.ceil(num_windows / rows)

            # Ensure the grid can actually fit all windows
            while rows * cols < num_windows:
                if rows <= cols:
                    rows += 1
                else:
                    cols += 1

            return rows, cols

    def display_results(self, message):
        """Display results with simple formatting."""
        self.console.print(f"\n{message}")

    def display_error(self, message):
        """Display error messages."""
        self.console.print(f"\n{message}")

    def display_info(self, message):
        """Display info messages."""
        self.console.print(f"{message}")

    def show_header(self, title):
        """Display a simple header."""
        self.console.print(f"\n{title}")
        self.console.print("-" * len(title))

# ---------------------------------------------------------
# 5) WindowTilerApp
# ---------------------------------------------------------
class WindowTilerApp:
    def __init__(self, config=None):
        self.config = config or Config()
        self.windowManager = WindowManager(self.config)
        self.monitorManager = MonitorManager()
        self.layoutManager = LayoutManager()
        self.ui = UserInterface()
        self.running = True

    def run_interactive(self):
        """Run the application in interactive CLI mode."""
        self.ui.show_header("Window Tiler - Interactive Mode")
        self.ui.console.print("Welcome to Window Tiler! Type 'help' for available commands.")

        while self.running:
            try:
                self.ui.console.print()  # Empty line for spacing
                command = Prompt.ask("[bold cyan]WindowTiler[/bold cyan]", default="").strip().lower()

                if not command:
                    continue

                if command in ['exit', 'quit', 'q']:
                    self.ui.console.print("Goodbye!")
                    break
                elif command in ['help', 'h', '?']:
                    self.show_help()
                elif command in ['tile', 't']:
                    self.run_tiling_workflow()
                elif command in ['list', 'l']:
                    self.list_windows()
                elif command in ['monitors', 'm']:
                    self.list_monitors()
                elif command in ['clear', 'cls']:
                    self.ui.console.clear()
                else:
                    self.ui.console.print(f"[red]Unknown command: {command}[/red]")
                    self.ui.console.print("Type 'help' for available commands.")

            except KeyboardInterrupt:
                self.ui.console.print("\n[yellow]Use 'exit' to quit properly.[/yellow]")
            except Exception as e:
                self.ui.console.print(f"[red]Error: {e}[/red]")

    def show_help(self):
        """Display help information."""
        self.ui.console.print("\n[bold]Available Commands:[/bold]")
        self.ui.console.print("  [cyan]tile, t[/cyan]     - Start window tiling workflow")
        self.ui.console.print("  [cyan]list, l[/cyan]     - List all detected windows")
        self.ui.console.print("  [cyan]monitors, m[/cyan] - List available monitors")
        self.ui.console.print("  [cyan]clear, cls[/cyan]  - Clear the screen")
        self.ui.console.print("  [cyan]help, h, ?[/cyan]  - Show this help")
        self.ui.console.print("  [cyan]exit, quit, q[/cyan] - Exit the application")

    def list_windows(self):
        """List all detected windows."""
        self.ui.display_info("Scanning for windows...")
        self.windowManager.detect_windows()
        windows = self.windowManager.windows

        if not windows:
            self.ui.display_error("No windows found!")
            return

        self.ui.console.print(f"\n[bold]Found {len(windows)} windows:[/bold]")

        # Group by process for better organization
        grouped = self.windowManager.get_windows_by_process()
        for process_name, process_windows in sorted(grouped.items()):
            self.ui.console.print(f"\n[yellow]{process_name}[/yellow] ({len(process_windows)} windows):")
            for i, window in enumerate(process_windows, 1):
                title = window.title[:50] + "..." if len(window.title) > 50 else window.title
                self.ui.console.print(f"  {i}. {title}")

    def list_monitors(self):
        """List all available monitors."""
        self.monitorManager.detect_monitors()
        monitors = self.monitorManager.monitors

        if not monitors:
            self.ui.display_error("No monitors found!")
            return

        self.ui.console.print(f"\n[bold]Found {len(monitors)} monitor(s):[/bold]")
        for i, mon in enumerate(monitors):
            dims = mon.get_dimensions()
            work_dims = mon.get_work_dimensions()
            primary_txt = " [PRIMARY]" if mon.is_primary else ""
            self.ui.console.print(f"  {i + 1}. {mon.device} - {dims['width']}x{dims['height']} (work: {work_dims['width']}x{work_dims['height']}){primary_txt}")

    def run_tiling_workflow(self):
        """Run the complete tiling workflow."""
        return self.run()

    def run(self):
        self.ui.display_info("Finding windows...")

        self.windowManager.detect_windows()
        windows = self.windowManager.windows

        if not windows:
            self.ui.display_error("No windows found!")
            return

        self.ui.display_info(f"Total windows found: {len(windows)}")

        # Step: Choose grouping style
        self.ui.console.print("\nChoose grouping style:")
        self.ui.console.print(f"1) Tile all {len(windows)} windows")
        self.ui.console.print("2) By process name (e.g. 'chrome.exe')")
        self.ui.console.print("3) By window type (BROWSER, TERMINAL, EDITOR, etc.)")
        choice = Prompt.ask("Enter [1/2/3]", choices=["1", "2", "3"], default="2")

        if choice == "1":
            grouped = self.windowManager.get_all_windows_as_group()
        elif choice == "2":
            grouped = self.windowManager.get_windows_by_process()
        else:
            grouped = self.windowManager.get_windows_by_type()

        # Display group info
        group_keys = self.ui.show_grouped_windows(grouped)
        if not group_keys:
            self.ui.display_error("No groups found!")
            return

        # Step: Pick group
        chosen = self.ui.get_user_choice_index()
        try:
            chosen_idx = int(chosen) - 1  # Convert from 1-based to 0-based indexing
            if chosen_idx < 0 or chosen_idx >= len(group_keys):
                raise ValueError("Index out of range")
            group_key = group_keys[chosen_idx]
        except (ValueError, IndexError):
            self.ui.display_error("Invalid choice, quitting.")
            return

        # Step: Layout config
        windows_to_tile = grouped[group_key]
        rows, cols, restore_minimized, bring_to_front, use_work_area = self.ui.get_layout_configuration(len(windows_to_tile))

        # Step: Monitor selection
        self.monitorManager.detect_monitors()
        monitors = self.monitorManager.monitors
        if not monitors:
            self.ui.display_error("No monitors found!")
            return

        self.ui.show_monitor_selection(monitors)
        index_str = self.ui.prompt_monitor_index()

        if not index_str:
            monitor = self.monitorManager.get_primary_monitor()
        else:
            try:
                idx = int(index_str) - 1  # Convert from 1-based to 0-based indexing
                monitor = self.monitorManager.get_monitor_by_index(idx)
                if not monitor:
                    raise ValueError("Invalid monitor index")
            except (ValueError, IndexError):
                self.ui.display_error("Invalid input, defaulting to primary monitor.")
                monitor = self.monitorManager.get_primary_monitor()

        if not monitor:
            monitor = monitors[0]

        # Step: Do the layout
        self.layoutManager.apply_grid_layout(
            windows_to_tile,
            monitor,
            rows,
            cols,
            restore_minimized=restore_minimized,
            bring_to_front=bring_to_front,
            use_work_area=use_work_area
        )

        self.ui.display_results("Done.")

    def exit(self):
        """Clean up resources if needed and exit."""
        self.running = False

# ---------------------------------------------------------
# 6) Entry Point
# ---------------------------------------------------------
def main():
    app = WindowTilerApp()
    app.run()

if __name__ == "__main__":
    main()
