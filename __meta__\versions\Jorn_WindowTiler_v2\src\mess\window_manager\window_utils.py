# window_utils.py

"""
For utility functions like getting shell windows instances.
"""

# Import modules
import os
import sys
import re
import time
import random
import ctypes
import urllib.parse
import json
from enum import Enum

# Error handling
# import traceback
# import pywintypes

# Import pywin32 modules
import win32com.client
import win32com.shell.shellcon as shellcon
import win32con
import win32gui
import win32api
import win32process

# Import constants from win32con

from .window_enums import WindowExplorerType
from .window_enums import WindowCategory, WindowExplorerType


def get_shell_windows_instance():
    """
    "Shell.Application" refers to a COM class that provides access
    to the top-level object of the Windows Shell (shell32.dll), which includes
    functionality related to special folders, file explorer windows, accessing
    folder view settings, desktop, taskbar, etc.

    The following steps are taken:
    - Create and return a ShellObject instance of the Shell.Application ProgID.
    - Create a ShellWindows object (contains all open shell-window instances).
    - Dictionary map {HWND:SHELL} to enable shell-instances to be reached by HWND.
    """
    shell_object_instance = win32com.client.Dispatch('Shell.Application')
    shell_window_instances = shell_object_instance.Windows()
    shell_window_mapping = {shell.HWND: shell for shell in shell_window_instances}

    """
    "Special Folder" refers to a folder represented by an interface
    rather than a specific path (e.g. 'Desktop', 'Control Panel', etc). They are
    identified by unique constants called 'CSIDL' (Constant Special Item ID List).

    The following steps are taken:
    - Use 'shellcon' to list the 'CSIDL' identifiers and retrieve their constants.
    - Use 'shell_object_instance' to create shell object namespaces for each constant.
    - Filter out any invalid namespaces (without a shell Application property).
    - Retrieve identifier ('Name') and path ('Path') from each namespace.
    - Dictionary map {'Name':'Path'} to enable path to be reached from 'title'.
    """
    CSIDL_CONSTANTS = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]
    csidl_namespaces = [shell_object_instance.Namespace(constant) for constant in CSIDL_CONSTANTS]
    valid_namespaces = [namespace for namespace in csidl_namespaces if hasattr(namespace, 'Application')]
    special_folders = [[namespace.Self.Name, namespace.Self.Path] for namespace in valid_namespaces]
    special_folders_mapping = {item[0]: item[1] for item in special_folders}

    # Return the result
    return shell_window_mapping, special_folders_mapping


def get_explorer_windows(shell_window_mapping, special_folders_mapping):
    """
    Parameters:
    - 'shell_window_mapping': Mapping of 'hwnd' to shell instance, used to
       obtain the path of the folder in case of 'NORMAL'.
    - 'special_folders_mapping': Mapping of 'title' to 'special folder' path, used to
       obtain the path of folder in case of 'SPECIAL_FOLDER'.

    This function updates the following instance variables:
    - 'process': The process associated with the window.
    - 'process_id': The process id associated with the window.
    - 'type': The type of the window ('SPECIAL_FOLDER', 'NORMAL', 'UNSPECIFIED', 'UNLINKED', 'UNKNOWN').
    - 'create_cmd': The command to open the window, if applicable.
    """
    # If this is a Windows File-Explorer Window (typically a folder/directory)
    hwnd_class = win32gui.GetClassName(hwnd)
    if hwnd_class == 'CabinetWClass':
        # Retrieve the folder path through its shell instance
        hwnd_shell_instance = shell_window_mapping[hwnd]
        hwnd_shell_path = hwnd_shell_instance.LocationURL

        # If it's a 'SPECIAL_FOLDER' (explorer window without retrievable path):
        # - Update the instance variable 'type' with the identifier 'SPECIAL_FOLDER'.
        # - Check if the path refers to a GUID (global unique identification number).
        # - Transform the path into a cmd-command (using 'Shell:{GUID}' or 'File:/URI'),
        #   this is to make the path executable (in that it creates the actual window).
        # - Update the instance variable 'create_cmd' with the modified path-command.
        if hwnd_title in special_folders_mapping:
            hwnd_type = WindowExplorerType.SPECIAL_FOLDER
            folder_path = special_folders_mapping[hwnd_title]
            folder_path_guid_match = bool(re.search(r'::{[\w-]*}', folder_path))
            folder_path_is_guid = folder_path_guid_match if not os.path.exists(folder_path) else False
            command_prefix = 'Shell:' if folder_path_is_guid else 'File:/'
            create_command = os.path.normpath(urllib.parse.unquote(f'{command_prefix}{folder_path}'))
            hwnd_create_cmd = create_command

        # Else it's a 'NORMAL' (explorer window with a retrievable path):
        elif (hwnd_shell_path != ''):
            # Update the instance variable 'type' with the identifier 'NORMAL'.
            hwnd_type = WindowExplorerType.NORMAL
            # Update the instance variable 'create_cmd' with the path (URI).
            hwnd_create_cmd = os.path.normpath(urllib.parse.unquote(hwnd_shell_path))
            # Update the instance variables for folder view options.
            folder_obj = hwnd_shell_instance.Document
            folder_icon_size = folder_obj.IconSize
            folder_view_mode = folder_obj.CurrentViewMode
            folder_sort_column = folder_obj.SortColumns
            folder_group_by = folder_obj.GroupBy
            print(folder_group_by)
            # Update the instance variables for files in folder.
            folder_selected_files = [file.Name for file in folder_obj.SelectedItems()]
            folder_all_files = [file.Name for file in folder_obj.Folder.Items()]
            folder_focused_file = folder_obj.FocusedItem.Name if folder_obj.FocusedItem else None


    # If a title and class was found but no type has been retrieved (no associated window process):
    # - Set instance variable 'type' to 'UNLINKED'.
    if hwnd_title and hwnd_class and not hwnd_type:
        hwnd_type = WindowExplorerType.UNLINKED
