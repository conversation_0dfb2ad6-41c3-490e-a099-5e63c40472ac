@ECHO OFF
SETLOCAL ENABLEDELAYEDEXPANSION

:: =============================================================================
:: Main Script Logic
:: =============================================================================
CALL :InitBatchScript
CALL :SpecifyOptions
CALL :InitEnvironment
CALL :InitVenvPrompt
EXIT /B 0


:: =============================================================================
:: Subroutines
:: =============================================================================

:: Initialize script-specific variables.
:InitBatchScript
    :: Switch to user-provided directory or stay in the current directory.
    IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
    :: Stash away information; you never know when you'll need it.
    SET "CmdStartDirectory=%CD%"
    SET "CmdFullPath=%~dp0%~nx0"
    SET "CmdDirectory=%~dp0"
    SET "CmdFileName=%~nx0"
    SET "CmdBaseName=%~n0"
    SET "CmdExtension=%~x0"
GOTO :EOF

:: Specify script options.
:SpecifyOptions
    SET "EchoOff=true"
    SET "PromptForWindowTitle=true"
GOTO :EOF


:: Prompt for window title.
:InitWindowTitle
    IF "%PromptForWindowTitle%" == "true" (
        ECHO.
        ECHO Specify the name of this window:
        SET /P WindowTitle="(e.g. console -> main.py): "
        TITLE !WindowTitle!
    )
GOTO :EOF

:: Locate and retrieve the project and it's venv.
:InitEnvironment
    :: Locate the project folder.
    SET "ProjectCacheIdentifier=.cache"
    :SearchProjectCache
        IF EXIST "%CD%\%ProjectCacheIdentifier%" (GOTO ReadProjectCache)
        SET "TempDir=%CD%" & CD ..
        IF "%CD%"=="%TempDir%" (
            CD /D "%CmdStartDirectory%"
            ECHO Could not find a project directory, make sure you've initialized it.
            GOTO :ExitScript
        )
    GOTO SearchProjectCache
    :: Read the project cache files.
    :ReadProjectCache
        SET "ProjectNameCache=%CD%\.cache\.PROJECT-NAME.%COMPUTERNAME%"
        SET "ProjectPathCache=%CD%\.cache\.PROJECT-PATH.%COMPUTERNAME%"
        SET "VenvDirPathCache=%CD%\.cache\.VENV-PATH.%COMPUTERNAME%"
        SET "VenvPyVersionCache=%CD%\.cache\.PYTHON-VERSION.%COMPUTERNAME%"
        IF EXIST "%ProjectNameCache%" (SET /P ProjectName=<"%ProjectNameCache%")
        IF EXIST "%ProjectPathCache%" (SET /P ProjectPath=<"%ProjectPathCache%")
        IF EXIST "%VenvDirPathCache%" (SET /P VenvDirPath=<"%VenvDirPathCache%")
        IF EXIST "%VenvPyVersionCache%" (SET /P VenvPyVersion=<"%VenvPyVersionCache%")
        CD /D "%CmdStartDirectory%"
        :: Initialize environment variables (or exit with information)
        IF EXIST "%ProjectPath%" (
            IF EXIST "%ProjectPath%" (
                SET "VenvRequirementsTxt=%ProjectPath%\requirements.txt"
                SET "VenvPyExe=%VenvDirPath%\Scripts\python.exe"
                SET "VenvPipExe=%VenvDirPath%\Scripts\pip.exe"
                SET "VenvActivate=%VenvDirPath%\Scripts\activate"
            )
        ) ELSE (
            IF NOT EXIST "%ProjectPath%" (SET "ProjectPathStatus=[ NOT FOUND! ] ->")
            IF NOT EXIST "%VenvDirPath%" (SET "VenvDirPathStatus=[ NOT FOUND! ] ->")
            ECHO Could not read valid paths from cache files.
            ECHO.
            ECHO - ProjectName      : !ProjectPathStatus! %ProjectName%
            ECHO - ProjectPath      : !ProjectPathStatus! %ProjectPath%
            ECHO - VenvDirPath      : !VenvDirPathStatus! %VenvDirPath%
            ECHO - VenvPyVersion    : !VenvDirPathStatus! %VenvPyVersion%
            GOTO :ExitScript
        )
GOTO :EOF

:: Specify script execution options
:InitVenvPrompt
    :: Display information
    ECHO -----------------------------------------------------------------------
    ECHO Information:
    ECHO - Project Name : %ProjectName%
    ECHO - Project Path : %ProjectPath%
    ECHO.
    ECHO - Venv Path    : %VenvDirPath%
    ECHO - Py Version   : %VenvPyVersion%
    ECHO.
    ECHO - Current Path : !CmdStartDirectory:%ProjectPath%=!\
    ECHO.
    ECHO -----------------------------------------------------------------------

    SET "EchoOff=" & IF "%EchoOff%"=="true" SET "EchoOff=& @ECHO OFF"
    CALL :InitWindowTitle

    :: Activate virtual environment
    :: SET "VenvActivateCmd="%VenvActivate%" & CD /D %CD% & TITLE ^(%VenvDirName%^) %CD%"
    :: CMD /K "%VenvActivateCmd%"
    CMD /K ""%VenvActivate%" & CD /D %CD% !EchoOff! & TITLE [%WindowTitle%] ^(venv^) %ProjectName%"
    :: CMD /K ""%Project_Venv_Activate%" & CD /D %CD% & TITLE (venv) %CD% - Prompt"
GOTO :EOF

