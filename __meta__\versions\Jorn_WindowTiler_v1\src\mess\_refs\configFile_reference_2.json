{"version": "*******", "description": "Window Layout Manager, or WiLMA for short.", "homepage": "http://www.stefandidak.com/windows-layout-manager/", "license": "Freeware", "url": "http://www.stefandidak.com/wilma/winlayoutmanager.zip", "hash": "c5edea1bc867a48cb0ad15d7f2a590143d2d8f8659715a33efa0b02fe6a4bcea", "pre_install": ["if(!(Test-Path \"$persist_dir\\config.xml\")) {", "  New-Item \"$persist_dir\\config.xml\" -Type File -Force | Out-Null", "  New-Item \"$persist_dir\\layout.dat\" -Type File -Force | Out-Null", "}"], "persist": ["config.xml", "layout.dat"], "shortcuts": [["WinLayoutManager.exe", "Windows Layout Manager"]], "checkver": {"regex": "New version ([\\d.]+)"}, "autoupdate": {"url": "http://www.stefandidak.com/wilma/winlayoutmanager.zip"}}