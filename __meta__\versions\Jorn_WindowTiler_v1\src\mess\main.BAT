@ECHO OFF
SETLOCAL ENABLEDELAYEDEXPANSION

:: =============================================================================
:: Main Script Logic
:: =============================================================================
CALL :InitBatchScript
CALL :SpecifyOptions
CALL :InitEnvironment
CALL :InitPyInputArgs
CALL :ExecutePyScript
EXIT /B 0


:: =============================================================================
:: Subroutines
:: =============================================================================

:: Initialize script-specific variables.
:InitBatchScript
    :: Switch to user-provided directory or stay in the current directory.
    IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
    :: Stash away information; you never know when you'll need it.
    SET "CmdStartDirectory=%CD%"
    SET "CmdFullPath=%~dp0%~nx0"
    SET "CmdDirectory=%~dp0"
    SET "CmdFileName=%~nx0"
    SET "CmdBaseName=%~n0"
    SET "CmdExtension=%~x0"
GOTO :EOF

:: Specify script options.
:SpecifyOptions
    SET "PromptForPyInputArgs=false"
    SET "ExitAfterSeconds=false"
GOTO :EOF

:: Locate and retrieve the project and it's venv.
:InitEnvironment
    :: Locate the project folder.
    SET "ProjectCacheIdentifier=.CACHE"
    :SearchProjectCache
        IF EXIST "%CD%\%ProjectCacheIdentifier%" (GOTO ReadProjectCache)
        SET "TempDir=%CD%" & CD ..
        IF "%CD%"=="%TempDir%" (
            CD /D "%CmdStartDirectory%"
            ECHO Could not find a project directory, make sure you've initialized it.
            PING 127.0.0.1 -n 10 > NUL & EXIT
        )
    GOTO SearchProjectCache
    :: Read the project cache files.
    :ReadProjectCache
        SET "ProjectNameCache=%CD%\.CACHE\.PROJECT-NAME.%COMPUTERNAME%"
        SET "ProjectPathCache=%CD%\.CACHE\.PROJECT-PATH.%COMPUTERNAME%"
        SET "VenvDirPathCache=%CD%\.CACHE\.VENV-PATH.%COMPUTERNAME%"
        SET "VenvPyVersionCache=%CD%\.CACHE\.PYTHON-VERSION.%COMPUTERNAME%"
        IF EXIST "%ProjectNameCache%" (SET /P ProjectName=<"%ProjectNameCache%")
        IF EXIST "%ProjectPathCache%" (SET /P ProjectPath=<"%ProjectPathCache%")
        IF EXIST "%VenvDirPathCache%" (SET /P VenvDirPath=<"%VenvDirPathCache%")
        IF EXIST "%VenvPyVersionCache%" (SET /P VenvPyVersion=<"%VenvPyVersionCache%")
        CD /D "%CmdStartDirectory%"
        :: Initialize environment variables or exit with information
        IF EXIST "%ProjectPath%" (
            IF EXIST "%ProjectPath%" (
                SET "VenvRequirementsTxt=%ProjectPath%\requirements.txt"
                SET "VenvPyExe=%VenvDirPath%\Scripts\python.exe"
                SET "VenvPipExe=%VenvDirPath%\Scripts\pip.exe"
                SET "VenvActivate=%VenvDirPath%\Scripts\activate"
            )
        ) ELSE (
            IF NOT EXIST "%ProjectPath%" (SET "ProjectPathStatus=[ NOT FOUND! ] ->")
            IF NOT EXIST "%VenvDirPath%" (SET "VenvDirPathStatus=[ NOT FOUND! ] ->")
            ECHO Could not read valid paths from cache files.
            ECHO.
            ECHO - ProjectName      : !ProjectPathStatus! %ProjectName%
            ECHO - ProjectPath      : !ProjectPathStatus! %ProjectPath%
            ECHO - VenvDirPath      : !VenvDirPathStatus! %VenvDirPath%
            ECHO - VenvPyVersion    : !VenvDirPathStatus! %VenvPyVersion%
            PING 127.0.0.1 -n 20 > NUL & EXIT
        )
GOTO :EOF

:: Prompt for input arguments.
:InitPyInputArgs
    IF "%PromptForPyInputArgs%" == "true" (
        ECHO.
        ECHO Enter arguments to pass into the script:
        SET /P PyInputArgs="Args: "
        echo !PyInputArgs!
    )

:: Specify script execution options
:ExecutePyScript
    :: Specify script execution options
    SET "PyFileName=%CmdBaseName%.py"
    :: SET "PyFileName=_src_main.py"
    SET "PyFilePath=%CmdStartDirectory%\%PyFileName%"
    SET "PyInputArgs=!PyInputArgs!"

    :: SET "PyInputArgs=C:\Users\<USER>\Desktop\PRJ\GIT\JHP\WORKFLOW\PRJ__WIP\Py_Prj - Bookmarks Shortcuts\src\all_combined_2023.11.11\all_from_myself_2023.11.11\"
    :: "%VenvPyExe%" "%PyFilePath%" "%PyInputArgs%"

    :: SET "PyInputArgs=C:\Users\<USER>\Desktop\PRJ\GIT\JHP\WORKFLOW\PRJ__WIP\Py_Prj - Bookmarks Shortcuts\src\all_combined_2023.11.11\all_chrome_tabs_2023.11.09"
    :: "%VenvPyExe%" "%PyFilePath%" "%PyInputArgs%"


    :: SET "PyInputArgs=C:\Users\<USER>\Desktop\PRJ\GIT\JHP\WORKFLOW\PRJ__WIP\Py_Prj - Bookmarks Shortcuts\src\all_combined_2023.11.11\all_chrome_tabs_2023.11.09\s"
    :: SET "PyInputArgs=C:\Users\<USER>\Desktop\PRJ\GIT\JHP\WORKFLOW\PRJ__WIP\Py_Prj - Bookmarks Shortcuts\src"
    :: SET "PyInputArgs=C:\Users\<USER>\Desktop\PRJ\GIT_DISCONNECTED\bookmarkfolderizer_py\Unsorted"
    :: SET "PyInputArgs=%CD%\urls_testing"
    :: SET "PyInputArgs=C:\Users\<USER>\Desktop\PRJ\GIT\JHP\WORKFLOW\PRJ__WIP\Py_Prj - Bookmarks Shortcuts\src\all_combined_2023.11.11\all_from_myself_2023.11.11"

    TITLE %PyFileName%
    :: ECHO %PyInputArgs%
    "%VenvPyExe%" "%PyFilePath%" "%PyInputArgs%"


    :: Execute script directly
    :: "%VenvPyExe%" "%PyFilePath%" %PyInputArgs_ToJSON%

    :: -> {1} Exit with message
    IF "%ExitAfterSeconds%" == "true" (
        SET "SecondsUntilExit=10"
        ECHO.
        ECHO.
        ECHO.
        ECHO Window will close automatically in %SecondsUntilExit% seconds ...
        PING 127.0.0.1 -n %SecondsUntilExit% > NUL
        EXIT
    )
    :: -> {2} Re-execute indefinitely
    IF "%ExitAfterSeconds%" == "false" (
        ECHO.
        ECHO.
        ECHO.
        ECHO Press a key to re-execute script ...
        PAUSE > NUL
        CLS
        GOTO :ExecutePyScript
    )
GOTO :EOF


