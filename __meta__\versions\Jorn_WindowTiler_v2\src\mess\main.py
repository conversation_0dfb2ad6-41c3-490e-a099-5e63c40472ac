# main.py

import argparse
import json
import os
import re
import requests
import time
import unicodedata

from log import logger
import utils
from window_manager import (
    window_utils as window_utils,
    _0_manager as manager,
    _1_fetch as fetch,
)
# from hwnd_components import window_manager


if __name__ == "__main__":
    window_manager = manager.WindowManager()
    windows =window_manager.wm_get_open_windows()
    print(windows)
    # a = fetch.get_all_windows()
    # print(a)

    # a = wm_get_open_windows(self)
    # print(a)
