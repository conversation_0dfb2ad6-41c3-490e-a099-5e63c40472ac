import win32gui
import win32con

def list_windows():
    """List all open windows with their titles."""
    def enum_window_titles(hwnd, titles):
        if win32gui.IsWindowVisible(hwnd):
            titles.append((hwnd, win32gui.GetWindowText(hwnd)))
    titles = []
    win32gui.EnumWindows(enum_window_titles, titles)
    return titles

def find_window(title):
    """Find a window by its title."""
    return win32gui.FindWindow(None, title)

def activate_window(hwnd):
    """Bring the window to the foreground."""
    win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
    win32gui.SetForegroundWindow(hwnd)

def maximize_window(hwnd):
    """Maximize the window."""
    win32gui.ShowWindow(hwnd, win32con.SW_MAXIMIZE)

def minimize_window(hwnd):
    """Minimize the window."""
    win32gui.ShowWindow(hwnd, win32con.SW_MINIMIZE)

def restore_window(hwnd):
    """Restore the window to its original size and position."""
    win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)

def move_resize_window(hwnd, x, y, width, height):
    """Move and resize the window."""
    win32gui.MoveWindow(hwnd, x, y, width, height, True)

def close_window(hwnd):
    """Close the window."""
    win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)

# Example usage
# Print all windows
print(list_windows())

# Find, activate, and maximize a window
hwnd = find_window("Notepad")
hwnd = find_window("Task Host Window")
if hwnd:
    activate_window(hwnd)
    maximize_window(hwnd)
