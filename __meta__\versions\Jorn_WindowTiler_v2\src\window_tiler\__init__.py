"""
Window Tiler

A utility for dynamically arranging windows on Windows operating systems based on their type 
(process name, window class), enabling efficient workspace organization across multiple monitors.
"""

from window_tiler.core import (
    # Monitor components
    Monitor,
    get_all_monitors,
    get_primary_monitor,
    
    # Window components
    Window,
    get_all_windows,
    get_windows_by_type,
    
    # Tiler components
    Tiler,
    tile_windows,
    
    # Type definitions
    WindowType,
    WindowState
)

__version__ = '1.0.0'
__author__ = 'Window Tiler Team'

__all__ = [
    # Monitor components
    'Monitor',
    'get_all_monitors',
    'get_primary_monitor',
    
    # Window components
    'Window',
    'get_all_windows',
    'get_windows_by_type',
    
    # Tiler components
    'Tiler',
    'tile_windows',
    
    # Type definitions
    'WindowType',
    'WindowState',
    
    # Version information
    '__version__',
    '__author__'
]
