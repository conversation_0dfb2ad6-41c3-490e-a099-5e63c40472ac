# Distilled Context: Windows Window Tiler

## Core Mission
Create a utility that dynamically arranges windows on Windows operating systems based on their type (process name, window class), enabling efficient workspace organization across multiple monitors.

## Non-negotiable Constraints
1. Windows API integration must be reliable and robust
2. Window filtering by type must be accurate and flexible
3. Code must be maintainable and follow clean architecture principles

## Current Phase Focus
Consolidate multiple existing implementations into a single, coherent codebase that preserves all useful functionality while eliminating redundancy and improving window type detection capabilities.

## High-Impact Enhancement Opportunity
Implement a unified window filtering mechanism that combines process-based and class-based identification, enabling precise targeting of windows by application type for more intelligent tiling scenarios.
