# Window Tiler Usage Examples

This document provides practical examples of using the Window Tiler library for various window management scenarios.

## Basic Examples

### Listing All Windows

```python
from window_tiler.core import get_all_windows

# Get all visible windows with titles
all_windows = get_all_windows(visible_only=True, with_titles_only=True)

# Print window information
for hwnd, window in all_windows.items():
    print(f"Window: {window.title} (Handle: {window.hwnd})")
```

### Listing Monitors

```python
from window_tiler.core import get_all_monitors

# Get all monitors
monitors = get_all_monitors()

# Print monitor information
for handle, monitor in monitors.items():
    dims = monitor.get_dimensions()
    primary = "Primary" if monitor.is_primary else "Secondary"
    print(f"{primary} Monitor: {dims['width']}x{dims['height']} - {monitor.device}")
```

## Window Filtering Examples

### Filter Windows by Process Name

```python
from window_tiler.core import get_all_windows

# Get all windows
windows = get_all_windows(detect_types=True)

# Filter windows by process name
chrome_windows = []
for window in windows.values():
    if window.process_info and 'chrome.exe' in window.process_info.get('name', '').lower():
        chrome_windows.append(window)
        
print(f"Found {len(chrome_windows)} Chrome windows")
```

### Using Built-in Window Type Filters

```python
from window_tiler.core import get_windows_by_type, WindowType

# Get all browser windows
browser_windows = get_windows_by_type(window_type=WindowType.BROWSER_CHROME)

# Get all explorer windows
explorer_windows = get_windows_by_type(window_type=WindowType.EXPLORER_NORMAL)

# Get all terminal windows
terminal_windows = get_windows_by_type(window_type=WindowType.TERMINAL)

print(f"Found {len(browser_windows)} Chrome windows")
print(f"Found {len(explorer_windows)} Explorer windows")
print(f"Found {len(terminal_windows)} Terminal windows")
```

## Window Tiling Examples

### Basic Grid Tiling

```python
from window_tiler.core import get_all_monitors, get_windows_by_type, WindowType, Tiler

# Get primary monitor
monitors = get_all_monitors()
primary_monitor = next((m for m in monitors.values() if m.is_primary), None)

if primary_monitor:
    # Get all browser windows
    browser_windows = get_windows_by_type(window_type=WindowType.BROWSER)
    
    # Create tiler
    tiler = Tiler(monitors)
    
    # Tile browser windows in a 2x2 grid
    tiler.tile_grid(primary_monitor, browser_windows, rows=2, columns=2)
    print(f"Tiled {len(browser_windows)} browser windows in a 2x2 grid")
else:
    print("No monitors found")
```

### Custom Grid with Size Ratios

```python
from window_tiler.core import get_all_monitors, get_windows_by_type, WindowType, Tiler

# Get primary monitor
monitors = get_all_monitors()
primary_monitor = next((m for m in monitors.values() if m.is_primary), None)

if primary_monitor:
    # Get explorer windows
    windows = get_windows_by_type(window_type=WindowType.EXPLORER_NORMAL)
    
    # Define custom column and row ratios
    column_ratios = [0.6, 0.4]  # First column takes 60%, second takes 40%
    row_ratios = [0.3, 0.7]     # First row takes 30%, second takes 70%
    
    # Create tiler and arrange windows
    tiler = Tiler(monitors)
    tiler.tile_grid(primary_monitor, windows, rows=2, columns=2, 
                   column_ratios=column_ratios, row_ratios=row_ratios)
    
    print(f"Tiled {len(windows)} windows with custom size ratios")
```

### Split Layout (Primary + Secondary Windows)

```python
from window_tiler.core import get_all_monitors, get_all_windows, Tiler

# Get primary monitor
monitors = get_all_monitors()
primary_monitor = next((m for m in monitors.values() if m.is_primary), None)

if primary_monitor:
    # Get all windows
    all_windows = list(get_all_windows().values())
    
    if len(all_windows) >= 3:
        # Use first window as primary, rest as secondary
        primary_window = all_windows[0]
        secondary_windows = all_windows[1:3]  # Limit to 2 secondary windows
        
        # Create tiler
        tiler = Tiler(monitors)
        
        # Create split layout (primary window on left, secondary windows stacked on right)
        tiler.split(primary_monitor, primary_window, secondary_windows, 
                   primary_ratio=0.7, orientation='vertical')
        
        print("Created split layout with primary and secondary windows")
```

### Cascade Windows

```python
from window_tiler.core import get_all_monitors, get_windows_by_type, WindowType, Tiler

# Get primary monitor
monitors = get_all_monitors()
primary_monitor = next((m for m in monitors.values() if m.is_primary), None)

if primary_monitor:
    # Get document windows
    document_windows = get_windows_by_type(window_type=WindowType.DOCUMENT)
    
    # Create tiler
    tiler = Tiler(monitors)
    
    # Cascade windows with 30px offset
    tiler.cascade(primary_monitor, document_windows, offset_x=30, offset_y=30)
    
    print(f"Cascaded {len(document_windows)} document windows")
```

## Complex Example

This example shows how to create a workspace with code editor, browsers, and terminals arranged in specific ways:

```python
from window_tiler.core import get_all_monitors, get_windows_by_type, WindowType, Tiler

# Get monitors
monitors = get_all_monitors()
primary_monitor = next((m for m in monitors.values() if m.is_primary), None)
secondary_monitor = next((m for m in monitors.values() if not m.is_primary), None)

# Create tiler
tiler = Tiler(monitors)

# Setup primary monitor with code editor
if primary_monitor:
    # Get code editors and arrange them on the left side
    editors = get_windows_by_type(window_type=WindowType.EDITOR)
    if editors:
        primary_editor = editors[0]
        tiler.tile_grid(primary_monitor, [primary_editor], rows=1, columns=1, 
                        column_ratios=[1.0], row_ratios=[1.0])

# Setup secondary monitor with browser and terminal split
if secondary_monitor:
    # Get browsers and terminals
    browsers = get_windows_by_type(window_type=WindowType.BROWSER)
    terminals = get_windows_by_type(window_type=WindowType.TERMINAL)
    
    if browsers and terminals:
        # Use first browser as primary, terminals as secondary
        primary_browser = browsers[0]
        
        # Split screen: browser on top (60%), terminals on bottom (40%)
        tiler.split(secondary_monitor, primary_browser, terminals, 
                   primary_ratio=0.6, orientation='horizontal')
