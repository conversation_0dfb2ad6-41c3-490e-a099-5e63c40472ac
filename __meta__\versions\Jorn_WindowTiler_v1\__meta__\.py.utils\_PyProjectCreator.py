import os
import re
import json
import argparse
import logging
import shutil
import time

# Configure logging
logging.basicConfig(level=logging.INFO)

class ProjectSetup:

    def __init__(self, base_path, venv_path):
        self.venv_path = self._make_sublime_paths_relative(venv_path)

        self.base_path = base_path
        self.base_name = os.path.basename(base_path)

        # meta (this sublevel acts as a parent-interface to the utility itself)
        # - e.g: "Py_Jorn_Workflow_BookmarkFolderizer" ==> "bookmarkfolderizer_py"
        self.project_name = self._generate_project_dir_name(self.base_name)

        self.project_path = os.path.join(self.base_path, self.project_name)

    @staticmethod
    def _make_sublime_paths_relative(path):
        new_path = os.path.abspath(path).replace("\\", "/")
        for env_alias in ['APPDATA', 'USERPROFILE']:
            env_alias = env_alias.upper()
            env_path = os.path.abspath(os.environ[env_alias])
            env_path_normalized = os.path.abspath(env_path).replace("\\", "/")
            new_path = new_path.replace(env_path_normalized, f'%{env_alias}%')
        return new_path

    # @staticmethod
    # def _generate_project_dir_name(name):
    #     """
    #     This will be executed from within the
    #     example:
    #     """
    #     name_stripped = name.split(' - ', 1)[-1] if ' - ' in name else name
    #     name_with_underscores = re.sub(r"\s+", "_", name_stripped)
    #     alphanumeric_name = re.sub(r"[^a-zA-Z0-9 ]", "_", name_with_underscores)
    #     sanitized_name = re.sub(r"_{2,}", "_", alphanumeric_name)
    #     return f"{sanitized_name.lower()}"

    @staticmethod
    def _generate_project_dir_name(name):
        patterns=['py']
        strip_prefix_1 = True
        strip_prefix_2 = True
        case_sensitive=False
        move_to_end=True

        prj_name = name
        # print(f'prj_name: {prj_name}')
        prj_name = prj_name.split('__') [-1] if strip_prefix_1 else prj_name
        for lang in patterns:
            new_check = prj_name if case_sensitive else prj_name.lower()
            lang_check = lang if case_sensitive else lang.lower()
            str_parts = new_check.split('_')
            if lang_check in str_parts:
                prj_name = new_check.replace(f'{lang_check}_', '').replace(f'_{lang_check}', '')
                prj_name = prj_name.split('_') [-1] if strip_prefix_2 else prj_name
                if move_to_end:
                    prj_name = f'{prj_name}_{lang_check}'
                else:
                    prj_name = f'{lang_check}_{prj_name}'
        # print(f'prj_name: {prj_name}')
        return prj_name

    @staticmethod
    def _create_dir(path):
        if not os.path.exists(path):
            os.makedirs(path)
            logging.info(f"Created: {os.path.normpath(path)}")

    @staticmethod
    def _create_file(path):
        if not os.path.exists(path):
            open(path, 'a').close()
            logging.info(f"Created: {os.path.normpath(path)}")

    def _build_project_structure(self):
        self.project_structure = [
            {
                'path': '.',
                'files': ['README.md', f'main.py']
            },
            {
                'path': f'{self.project_name.lower()}/utils',
                'files': ['__init__.py', 'utility_functions.py']
            },
            # {
            #     'path': 'tests',
            #     'files': ['__init__.py']
            # },
            # {
            #     'path': 'logs',
            #     'files': ['application.log']
            # },
        ]
        for entry in self.project_structure:
            dir_path = os.path.join(self.project_path, entry['path'])
            self._create_dir(dir_path)
            for file_name in entry['files']:
                self._create_file(os.path.join(dir_path, file_name))

    def _build_meta_structure(self):
        # [DIRECTORY THAT IS DISCONNECTED FROM GIT]
        # - Used for testing without getting unstaged changes in git
        path_userprofile = os.path.abspath(os.environ['USERPROFILE'])
        path_userprofile_nrm = path_userprofile.replace("\\", "/")
        local_root_name = "GIT_DISCONNECTED"
        # print(f'self.project_name: {self.project_name}')
        local_root_path = f"{path_userprofile_nrm}/Desktop/PRJ/{local_root_name}"
        local_path = f"{local_root_path}/{self.project_name}"
        self._create_dir(local_path)
        self._create_file(os.path.join(local_path, 'GIT_DISCONNECTED'))

        # - Backups
        backups_dir_name = f"{'_backups'}"
        backups_dir_path = os.path.join(self.base_path, backups_dir_name)
        self._create_dir(backups_dir_path)
        self._create_file(os.path.join(backups_dir_path, backups_dir_name))

        # - Tests
        # tests_dir_name = f"{'_tests'}"
        # tests_dir_path = os.path.join(self.base_path, tests_dir_name)
        # self._create_dir(tests_dir_path)
        # self._create_file(os.path.join(tests_dir_path, tests_dir_name))

        # - References
        references_dir_name = f"{'_references'}"
        references_dir_path = os.path.join(self.base_path, references_dir_name)
        self._create_dir(references_dir_path)
        self._create_file(os.path.join(references_dir_path, references_dir_name))

        # - Notes
        notes_dir_name = f"{'_notes'}"
        notes_dir_path = os.path.join(self.base_path, notes_dir_name)
        self._create_dir(notes_dir_path)
        self._create_file(os.path.join(notes_dir_path, notes_dir_name))
        # - Notes -> Files
        self._create_file(os.path.join(notes_dir_path, f"{self.project_name}.notes.py"))

        # - Playground
        playground_dir_name = f"{'_playground'}"
        playground_dir_path = os.path.join(self.base_path, playground_dir_name)
        self._create_dir(playground_dir_path)
        self._create_file(os.path.join(playground_dir_path, playground_dir_name))
        self._create_file(os.path.join(playground_dir_path, f"{self.project_name}.PY"))
        # - Playground -> Files
        playground_utils_dir_path = os.path.join(playground_dir_path, 'CmdUtils')
        self._create_dir(playground_utils_dir_path)
        playground_bat_execute = os.path.join(playground_utils_dir_path, f"_EXECUTE.BAT")
        playground_bat_execute_1 = os.path.join(playground_dir_path, f"{self.project_name}.BAT")
        playground_bat_package = os.path.join(playground_utils_dir_path, f"_INSTALL_PACKAGE.BAT")
        playground_bat_venv_prompt = os.path.join(playground_utils_dir_path, f"_VENV_PROMPT.BAT")
        playground_bat_py_prompt = os.path.join(playground_utils_dir_path, f"_PY_PROMPT.BAT")
        shutil.copyfile(f"{self.base_path}/.venv/.cmd.utils/_EXECUTE_SCRIPT.bat", playground_bat_execute)
        shutil.copyfile(f"{self.base_path}/.venv/.cmd.utils/_EXECUTE_SCRIPT.bat", playground_bat_execute_1)
        shutil.copyfile(f"{self.base_path}/.venv/.cmd.utils/_INSTALL_REMOVE_PACKAGE.bat", playground_bat_package)
        shutil.copyfile(f"{self.base_path}/.venv/.cmd.utils/_OPEN_PROMPT_HERE.bat", playground_bat_venv_prompt)
        shutil.copyfile(f"{self.base_path}/.venv/.cmd.utils/_OPEN_PROMPT_HERE.bat", playground_bat_py_prompt)

        # - Sublime Project -> Main Project
        sublime_project_name = f"{self.project_name}.sublime-project"
        sublime_project_path = os.path.join(self.base_path, sublime_project_name)
        sublime_settings_json = {
            "folders": [
                {
                    "name": f"[ {self.base_name} ]",
                    "path": f"./.venv",
                    "folder_exclude_patterns": ["*"],
                    "file_exclude_patterns": ["*"],
                },
                {
                    "name": f"LOCAL - Not Tracked",
                    "path": f"{local_path}",
                },
                {
                    "name": f"Project - Root",
                    "path": f".",
                    "folder_exclude_patterns": [f"{self.project_name}"],
                },
                {
                    "name": f"Project - Code",
                    "path": f"{self.project_name}",
                },
            ],
            "build_systems": [
                {
                    "name": f"-> {self.project_name} \t <Py_Prj>",
                    "shell_cmd": f"\"{self.venv_path}/Scripts/python.exe\" -u \"$file\"",
                    "working_dir": "${folder:${project_path:${file_path}}}",
                    "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)",
                    "selector": "source.python",
                    # "variants": [
                    #     {
                    #         "name": "Debug",
                    #         "shell_cmd": f"\"{self.venv_path}\" -u \"$file\"",
                    #     }
                    # ]
                }
            ]
        }
        with open(sublime_project_path, 'w') as prj_file:
            json.dump(sublime_settings_json, prj_file, indent=4)
        logging.info(f"Created: {sublime_project_path}")

        # - Sublime Project -> Playground Project
        sublime_project_name = f"{self.project_name}_playground.sublime-project"
        sublime_project_path = os.path.join(self.base_path, sublime_project_name)
        sublime_settings_json = {
            "folders": [
                {
                    "name": f"[ {self.base_name} ]",
                    "path": f"./.venv",
                    "folder_exclude_patterns": ["*"],
                    "file_exclude_patterns": ["*"],
                },
                {
                    "name": f"Project - Playground",
                    "path": f"_playground",
                },
            ],
            "build_systems": [
                {
                    "name": f"-> {self.project_name} \t <Py_Prj>",
                    "shell_cmd": f"\"{self.venv_path}/Scripts/python.exe\" -u \"$file\"",
                    "working_dir": "${folder:${project_path:${file_path}}}",
                    "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)",
                    "selector": "source.python",
                }
            ]
        }
        with open(sublime_project_path, 'w') as prj_file:
            json.dump(sublime_settings_json, prj_file, indent=4)
        logging.info(f"Created: {sublime_project_path}")


    def execute(self):
        self._create_dir(self.project_path)
        self._build_project_structure()
        self._build_meta_structure()
        logging.info("Setup complete.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Set up a new project.")
    parser.add_argument("base_path", help="Base path for the new project")
    parser.add_argument("venv_path", help="Path to the Python virtual environment")
    args = parser.parse_args()

    ProjectSetup(args.base_path, args.venv_path).execute()
