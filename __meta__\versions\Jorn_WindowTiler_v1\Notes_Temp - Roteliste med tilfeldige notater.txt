----------------------------------------
def update_window(index):
    hwnd = hwnd_list[index]
    rect = window_rectangles[index]

    directions = [
        (-1, 0),  # Left
        (1, 0),   # Right
        (0, -1),  # Up
        (0, 1)    # Down
    ]

    for step in [-1, 1]:  # Step: -1 for shrinking, 1 for growing
        for direction in directions:
            current_rect = rect[:]

            while True:
                new_rect = [
                    current_rect[0] + step * direction[0],
                    current_rect[1] + step * direction[1],
                    current_rect[2] + step * direction[0],
                    current_rect[3] + step * direction[1]
                ]

                # Check if the new rectangle is within the screen bounds
                if new_rect[0] < 0 or new_rect[1] < 0 or new_rect[2] > screen_width or new_rect[3] > screen_height:
                    break

                # Check if the new rectangle intersects with any other window
                intersects = False
                for i, other_rect in enumerate(window_rectangles):
                    if i != index and has_intersection(new_rect, other_rect):
                        intersects = True
                        break

                # Check if the new rectangle reaches the screen edge
                reaches_edge = (
                    new_rect[0] == 0 or
                    new_rect[1] == 0 or
                    new_rect[2] == screen_width or
                    new_rect[3] == screen_height
                )

                if intersects or reaches_edge:
                    break
                else:
                    current_rect = new_rect

            window_rectangles[index] = current_rect

    # Update the window position and size after all directions have been considered
    new_rect = window_rectangles[index]
    win32gui.MoveWindow(hwnd, new_rect[0], new_rect[1], new_rect[2] - new_rect[0], new_rect[3] - new_rect[1], True)

for index in range(len(hwnd_list)):
    update_window(index)


----------------------------------------

"""
Are you able to create an algorithm which can snap multiple windows together
in a way that makes them fill the entire screen without overlapping?

--------------------
Sure! I'll provide you with a high-level description of an algorithm that can
snap multiple windows together to fill the entire screen without overlapping.
This algorithm assumes that you have a list of window handles(hwnd_list) that
need to be snapped together. Here's an outline of the algorithm:

Determine the screen dimensions (width and height).
Calculate the total number of windows to be snapped.
Find the optimal grid arrangement (number of rows and columns) for the windows based on their aspect ratios, screen dimensions, and desired layout (e.g., more rows than columns or vice versa).
For each window in hwnd_list, calculate its new size and position based on its position in the grid.
Move and resize each window to its new size and position, ensuring they fill the entire screen without overlapping.

--------------------
 You want to resize the windows in a way that mimics their "growth" in every direction until they can't grow further without overlapping other windows. Here's an improved version of the snap_windows_to_fill_screen function that implements this behavior:

--------------------
To handle edge cases where windows overlap and it might make more sense to shrink some windows, we can modify the grow_windows_incrementally function to implement a more adaptive algorithm. This new algorithm will include both growing and shrinking windows to maximize the covered area while preventing overlap.
--------------------
I'll modify the update_window function to grow or shrink windows in each direction independently until they reach another edge.
This updated function will grow or shrink each window in each direction independently until it reaches another window or the screen edge. This should handle the situation you described where one direction should stop, while the other directions continue to grow or shrink.
Now, the function checks if the new rectangle reaches the screen edge and stops growing or shrinking in that direction if it does.
--------------------
Fill the entire screen with windows, without leaving any gaps or overlaps.
Resize windows simultaneously, treating each window's edges with equal priority.
Windows should "meet each other halfway" when determining their final dimensions.
Each edge of a window should be "glued" to its intersection points with other windows or screen edges.
Do not maintain the original aspect ratio or size of the windows.
After the initial window resizing, ensure all available screen space is utilized.
Handle edge cases where it makes more sense to shrink some windows in certain directions rather than grow.
These requirements outline the desired behavior for a dynamic window tiling function that automatically resizes and arranges windows to fill the entire screen, considering each window's edges with equal priority and ensuring a balanced, intuitive, and predictable outcome.

--------------------
--------------------
--------------------
--------------------
--------------------
--------------------
--------------------
--------------------
--------------------
--------------------