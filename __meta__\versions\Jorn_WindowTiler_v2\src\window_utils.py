"""
Window Utilities for the Window Tiler application.

This module provides utility functions for working with Windows shell instances,
special folders, and other window-specific operations.
"""

import os
import re
import ctypes
import urllib.parse

import win32com.client
import win32com.shell.shellcon as shellcon
import win32api
import win32gui
import win32process
import win32con

from window_enums import WindowType, WindowExplorerType


def get_shell_windows_instance():
    """
    Retrieves Shell.Application instance and maps open shell windows.

    "Shell.Application" provides access to the top-level object of the Windows Shell,
    which includes functionality related to special folders, file explorer windows,
    accessing folder view settings, desktop, taskbar, etc.

    Returns:
        tuple: (shell_window_mapping, special_folders_mapping) where:
            - shell_window_mapping: Dictionary mapping HWNDs to shell window instances
            - special_folders_mapping: Dictionary mapping special folder names to their paths
    """
    # Create Shell.Application instance
    shell_object_instance = win32com.client.Dispatch('Shell.Application')
    shell_window_instances = shell_object_instance.Windows()
    
    # Create mapping of window handles to shell instances
    shell_window_mapping = {shell.HWND: shell for shell in shell_window_instances}

    """
    "Special Folder" refers to a folder represented by an interface
    rather than a specific path (e.g. 'Desktop', 'Control Panel', etc). They are
    identified by unique constants called 'CSIDL' (Constant Special Item ID List).
    """
    # Get CSIDL constants from shellcon
    CSIDL_CONSTANTS = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]
    
    # Create namespace objects for each constant
    csidl_namespaces = [shell_object_instance.Namespace(constant) for constant in CSIDL_CONSTANTS]
    
    # Filter out invalid namespaces
    valid_namespaces = [namespace for namespace in csidl_namespaces if hasattr(namespace, 'Application')]
    
    # Extract name and path for each special folder
    special_folders = [[namespace.Self.Name, namespace.Self.Path] for namespace in valid_namespaces]
    special_folders_mapping = {item[0]: item[1] for item in special_folders}

    return shell_window_mapping, special_folders_mapping


def get_window_process_info(hwnd):
    """
    Retrieves process information for a given window handle.
    
    Args:
        hwnd: Window handle to get process info for
        
    Returns:
        dict: Dictionary containing 'id', 'handle', and 'path' of the process
              Returns None if process info cannot be retrieved
    """
    try:
        # Get process ID and thread ID
        hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(hwnd)
        
        # Open process to get more information
        process_query_flags = win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ
        hwnd_process_handle = ctypes.windll.kernel32.OpenProcess(process_query_flags, False, hwnd_process_id)
        
        if hwnd_process_handle:
            # Get process executable path
            process_path = win32process.GetModuleFileNameEx(hwnd_process_handle, 0)
            process_name = os.path.basename(process_path)
            
            # Return collected information
            return {
                'id': hwnd_process_id,
                'handle': hwnd_process_handle,
                'path': process_path,
                'name': process_name
            }
    except Exception as e:
        print(f"Error getting process info for window {hwnd}: {str(e)}")
    
    return None


def get_explorer_window_info(hwnd, shell_window_mapping, special_folders_mapping):
    """
    Gets detailed information for Explorer windows.
    
    Args:
        hwnd: Window handle
        shell_window_mapping: Dictionary mapping HWNDs to shell window instances
        special_folders_mapping: Dictionary mapping special folder names to paths
        
    Returns:
        dict: Information about the explorer window including type, path, etc.
              Returns None if not an explorer window or if info cannot be retrieved
    """
    # Check if it's an Explorer window by class name
    hwnd_class = win32gui.GetClassName(hwnd)
    if hwnd_class != 'CabinetWClass':
        return None
        
    # Get window title
    hwnd_title = win32gui.GetWindowText(hwnd)
        
    result = {
        'hwnd': hwnd,
        'title': hwnd_title,
        'class': hwnd_class,
        'type': None,
        'path': None,
        'create_cmd': None,
        'folder_info': {}
    }
    
    try:
        # Get shell instance for this window
        shell_instance = shell_window_mapping.get(hwnd)
        if not shell_instance:
            result['type'] = WindowExplorerType.UNKNOWN
            return result
            
        # Get location URL
        location_url = shell_instance.LocationURL
        
        # Handle special folders
        if hwnd_title in special_folders_mapping:
            result['type'] = WindowExplorerType.SPECIAL_FOLDER
            folder_path = special_folders_mapping[hwnd_title]
            
            # Check if path is a GUID
            folder_path_guid_match = bool(re.search(r'::{[\w-]*}', folder_path))
            folder_path_is_guid = folder_path_guid_match if not os.path.exists(folder_path) else False
            
            # Create command to reopen this window
            command_prefix = 'Shell:' if folder_path_is_guid else 'File:/'
            create_command = os.path.normpath(urllib.parse.unquote(f'{command_prefix}{folder_path}'))
            
            result['path'] = folder_path
            result['create_cmd'] = create_command
            
        # Handle normal explorer windows
        elif location_url:
            result['type'] = WindowExplorerType.NORMAL
            folder_path = urllib.parse.unquote(location_url)
            folder_path = folder_path.replace('file:///', '')  # Remove protocol prefix
            
            result['path'] = os.path.normpath(folder_path)
            result['create_cmd'] = os.path.normpath(urllib.parse.unquote(location_url))
            
            # Get additional folder information if available
            try:
                folder_obj = shell_instance.Document
                result['folder_info'] = {
                    'icon_size': folder_obj.IconSize,
                    'view_mode': folder_obj.CurrentViewMode,
                    'sort_column': folder_obj.SortColumns,
                    'group_by': folder_obj.GroupBy,
                    'selected_files': [file.Name for file in folder_obj.SelectedItems()],
                    'all_files': [file.Name for file in folder_obj.Folder.Items()],
                    'focused_file': folder_obj.FocusedItem.Name if folder_obj.FocusedItem else None
                }
            except:
                pass
    except Exception as e:
        print(f"Error getting Explorer info for window {hwnd}: {str(e)}")
    
    return result


def determine_window_type(hwnd, process_info=None, explorer_info=None):
    """
    Determines the WindowType for a given window based on various characteristics.
    
    Args:
        hwnd: Window handle
        process_info: Optional process info from get_window_process_info
        explorer_info: Optional explorer window info from get_explorer_window_info
        
    Returns:
        WindowType: The determined window type enum value
    """
    if not hwnd:
        return WindowType.UNKNOWN
        
    # Initialize process info if not provided
    if not process_info:
        process_info = get_window_process_info(hwnd)
        
    # Get window class
    try:
        window_class = win32gui.GetClassName(hwnd)
        window_title = win32gui.GetWindowText(hwnd)
    except:
        return WindowType.UNKNOWN
        
    # Case 1: Explorer windows
    if explorer_info or window_class == 'CabinetWClass':
        if explorer_info and explorer_info.get('type') == WindowExplorerType.SPECIAL_FOLDER:
            return WindowType.EXPLORER_SPECIAL_FOLDER
        else:
            return WindowType.EXPLORER_NORMAL
        
    # Case 2: Browser windows
    if process_info and process_info.get('name'):
        process_name = process_info['name'].lower()
        if 'chrome.exe' in process_name:
            return WindowType.BROWSER_CHROME
        elif 'msedge.exe' in process_name:
            return WindowType.BROWSER_EDGE
        elif 'firefox.exe' in process_name:
            return WindowType.BROWSER_FIREFOX
        elif any(browser in process_name for browser in ['iexplore.exe', 'safari.exe', 'opera.exe']):
            return WindowType.BROWSER
            
    # Case 3: Terminal windows
    if process_info and process_info.get('name'):
        process_name = process_info['name'].lower()
        if any(term in process_name for term in ['cmd.exe', 'powershell.exe', 'windowsterminal.exe', 'bash.exe']):
            return WindowType.TERMINAL
            
    # Case 4: Text editors and IDEs
    if process_info and process_info.get('name'):
        process_name = process_info['name'].lower()
        if any(editor in process_name for editor in ['notepad.exe', 'code.exe', 'sublime_text.exe', 'atom.exe']):
            return WindowType.EDITOR
        elif any(ide in process_name for ide in ['devenv.exe', 'pycharm64.exe', 'eclipse.exe', 'idea64.exe']):
            return WindowType.IDE
            
    # Case 5: No process association
    if not process_info:
        return WindowType.UNLINKED
        
    # Default case
    return WindowType.NORMAL


def get_windows_by_type(window_type=None, process_name=None, window_class=None):
    """
    Returns windows filtered by specified criteria.
    
    Args:
        window_type (WindowType, optional): Filter by window type
        process_name (str, optional): Filter by process name
        window_class (str, optional): Filter by window class
        
    Returns:
        list: List of window handles matching the criteria
    """
    matching_windows = []
    shell_window_mapping, special_folders_mapping = get_shell_windows_instance()
    
    def enum_windows_callback(hwnd, results):
        # Skip invisible windows
        if not win32gui.IsWindowVisible(hwnd):
            return True
            
        # Skip windows without titles (typically system windows)
        if not win32gui.GetWindowText(hwnd):
            return True
            
        # Get process info
        process_info = get_window_process_info(hwnd)
        
        # Check process name filter if specified
        if process_name and process_info and process_info.get('name'):
            if process_name.lower() not in process_info['name'].lower():
                return True
                
        # Check window class filter if specified
        if window_class:
            try:
                if window_class != win32gui.GetClassName(hwnd):
                    return True
            except:
                return True
                
        # Get explorer info if needed
        explorer_info = None
        if window_type in [WindowType.EXPLORER_NORMAL, WindowType.EXPLORER_SPECIAL_FOLDER] or not window_type:
            explorer_info = get_explorer_window_info(hwnd, shell_window_mapping, special_folders_mapping)
            
        # Check window type filter if specified
        if window_type:
            detected_type = determine_window_type(hwnd, process_info, explorer_info)
            if detected_type != window_type:
                return True
                
        # Window passed all filters, add to results
        results.append(hwnd)
        return True
    
    # Enumerate all top-level windows
    win32gui.EnumWindows(enum_windows_callback, matching_windows)
    return matching_windows
