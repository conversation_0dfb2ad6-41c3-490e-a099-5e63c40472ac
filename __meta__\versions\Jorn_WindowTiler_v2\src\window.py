import win32gui
import win32con
import win32api


class Window:
    def __init__(self, hwnd):
        self.hwnd = hwnd
        self.update_dimensions()

    def update_dimensions(self):
        rect = win32gui.GetWindowRect(self.hwnd)
        self.dimensions = {
            "x": rect[0],
            "y": rect[1],
            "width": rect[2] - rect[0],
            "height": rect[3] - rect[1],
        }

    def tile(self, monitor, position: tuple, size: tuple):
        monitor_dimensions = monitor.get_dimensions()
        new_x = monitor.monitor_area[0] + int(position[0] * monitor_dimensions["width"])
        new_y = monitor.monitor_area[1] + int(
            position[1] * monitor_dimensions["height"]
        )
        new_width = int(size[0] * monitor_dimensions["width"])
        new_height = int(size[1] * monitor_dimensions["height"])

        win32gui.MoveWindow(self.hwnd, new_x, new_y, new_width, new_height, True)

    def get_rect(self):
        return win32gui.GetWindowRect(self.hwnd)

    def maximize_within_monitor(self, monitor):
        work_area = monitor.get_work_area()
        self.tile(monitor, (0, 0), (1, 1))

    def center_within_monitor(self, monitor):
        monitor_dimensions = monitor.get_dimensions()
        window_width = self.dimensions["width"]
        window_height = self.dimensions["height"]
        x_position = (monitor_dimensions["width"] - window_width) // 2
        y_position = (monitor_dimensions["height"] - window_height) // 2
        self.tile(
            monitor,
            (
                x_position / monitor_dimensions["width"],
                y_position / monitor_dimensions["height"],
            ),
            (
                window_width / monitor_dimensions["width"],
                window_height / monitor_dimensions["height"],
            ),
        )

    def toggle_visibility(self):
        if self.is_visible():
            win32gui.ShowWindow(self.hwnd, win32con.SW_HIDE)
        else:
            win32gui.ShowWindow(self.hwnd, win32con.SW_SHOW)

    def is_focused(self):
        return self.hwnd == win32gui.GetForegroundWindow()

    def bring_to_front(self):
        win32gui.SetWindowPos(
            self.hwnd,
            win32con.HWND_TOP,
            self.dimensions["x"],
            self.dimensions["y"],
            self.dimensions["width"],
            self.dimensions["height"],
            win32con.SWP_NOACTIVATE,
        )

    def resize_and_move(self, x, y, width, height):
        win32gui.MoveWindow(self.hwnd, x, y, width, height, True)

    def is_full_screen(self):
        screen_width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
        screen_height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)
        rect = self.get_rect()
        return (
            rect[0] == 0
            and rect[1] == 0
            and rect[2] == screen_width
            and rect[3] == screen_height
        )

    def is_visible(self):
        return win32gui.IsWindowVisible(self.hwnd)


def get_all_windows():
    windows = {}

    def enum_windows(hwnd, result):
        if win32gui.IsWindowVisible(hwnd) and win32gui.GetWindowText(hwnd):
            windows[hwnd] = Window(hwnd)

    win32gui.EnumWindows(enum_windows, [])
    return windows


print(dir(get_all_windows))
