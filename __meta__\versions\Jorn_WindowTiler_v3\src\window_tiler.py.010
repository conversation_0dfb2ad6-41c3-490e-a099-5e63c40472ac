#!/usr/bin/env python3
"""
Refactored Window Tiler (Now Includes Explorer.exe, With Extra "Real Window" Checks)

Key points:
- Enumerates top-level "Alt+Tab style" windows (including minimized),
  but adds extra checks so only truly "real" application windows appear.
- Groups them by process name OR by a simple "window type".
- Lets you pick which group to tile.
- Optionally tries to unminimize windows (SW_SHOWNORMAL + SetWindowPos).
- Uses class-based architecture for clearer design.

Requirements:
    pip install pywin32 psutil
"""

import os
import sys
import ctypes
import math
from ctypes import wintypes
import psutil

import win32api
import win32gui
import win32con
import win32process

from enum import Enum, auto

# --------------------------------------------------------------------------------
# 0) Extra Cloaking Detection (for Windows 8+ / 10+)
# --------------------------------------------------------------------------------
# This helps skip "cloaked" windows, which are never actually visible to the user.

try:
    dwmapi = ctypes.WinDLL("dwmapi", use_last_error=True)
    DWMWA_CLOAKED = 14

    def is_window_cloaked(hwnd):
        """Return True if the window is cloaked (not actually visible), else False."""
        cloaked = wintypes.INT(0)
        res = dwmapi.DwmGetWindowAttribute(
            hwnd,
            DWMWA_CLOAKED,
            ctypes.byref(cloaked),
            ctypes.sizeof(cloaked)
        )
        if res == 0 and cloaked.value != 0:
            return True
        return False
except OSError:
    # If DWM APIs aren't available (older OS?), fallback to a dummy function
    def is_window_cloaked(hwnd):
        return False

# --------------------------------------------------------------------------------
# Global Helper (Needed for older PyWin32 if GetShellWindow doesn't exist)
# --------------------------------------------------------------------------------
user32 = ctypes.WinDLL("user32", use_last_error=True)
GetShellWindow = user32.GetShellWindow
GetShellWindow.restype = wintypes.HWND
SHELL_WINDOW = GetShellWindow()

# ---------------------------------------------------------
# Enumerations & Basic Classes
# ---------------------------------------------------------
class WindowType(Enum):
    BROWSER = auto()
    TERMINAL = auto()
    EXPLORER = auto()
    EDITOR = auto()
    IDE = auto()
    NORMAL = auto()
    UNKNOWN = auto()

class Monitor:
    """Represents a physical display monitor."""

    def __init__(self, handle, info):
        self.handle = handle
        self.is_primary = bool(info["Flags"] == 1)
        self.device = info["Device"]
        # Monitor area (left, top, right, bottom)
        mon_rect = info["Monitor"]
        work_rect = info["Work"]
        self.monitor_area = mon_rect
        self.work_area = work_rect

    def get_dimensions(self):
        left, top, right, bottom = self.monitor_area
        return {"width": right - left, "height": bottom - top}

# ---------------------------------------------------------
# Existing Window, Monitor Classes
# ---------------------------------------------------------
class Window:
    """
    Represents a single top-level window.
    Relies on external detection/heuristics but offers basic manipulation.
    """
    def __init__(self, hwnd, exe_path, rect):
        self.hwnd = hwnd
        self.title = win32gui.GetWindowText(hwnd)
        self.class_name = win32gui.GetClassName(hwnd)
        self.exe_path = exe_path  # full path from psutil
        self.process_name = os.path.basename(exe_path).lower() if exe_path else ""
        self.window_type = WindowType.UNKNOWN

        # rect: (left, top, right, bottom) from window placement
        self.rect = rect
        self._classify()

    def _classify(self):
        """Rough classification by process name."""
        pname = self.process_name
        cname = self.class_name.lower()

        if any(b in pname for b in ["chrome.exe", "msedge.exe", "firefox.exe", "iexplore.exe"]):
            self.window_type = WindowType.BROWSER
        elif any(t in pname for t in ["cmd.exe", "powershell.exe", "windowsterminal.exe"]):
            self.window_type = WindowType.TERMINAL
        elif "explorer.exe" in pname or "cabinetwclass" in cname:
            self.window_type = WindowType.EXPLORER
        elif any(e in pname for e in ["notepad.exe", "code.exe", "sublime_text.exe"]):
            self.window_type = WindowType.EDITOR
        elif any(i in pname for i in ["devenv.exe", "pycharm", "idea64"]):
            self.window_type = WindowType.IDE
        else:
            self.window_type = WindowType.NORMAL

    def move_and_resize(self, x, y, width, height, restore_minimized=True, bring_to_front=False):
        """Position and optionally restore the window."""
        was_minimized = win32gui.IsIconic(self.hwnd)

        if restore_minimized and was_minimized:
            # SW_SHOWNORMAL is more reliable than SW_RESTORE for typical apps
            win32gui.ShowWindow(self.hwnd, win32con.SW_SHOWNORMAL)

        # Move it
        win32gui.MoveWindow(self.hwnd, x, y, width, height, True)

        # Bring to front if requested (apply to all windows when enabled, not just restored ones)
        if bring_to_front:
            self._force_window_to_front(x, y, width, height)
        else:
            # Standard positioning without stealing focus
            win32gui.SetWindowPos(
                self.hwnd,
                win32con.HWND_TOP,
                x, y, width, height,
                win32con.SWP_NOSENDCHANGING | win32con.SWP_SHOWWINDOW
            )

    def _force_window_to_front(self, x, y, width, height):
        """Use multiple methods to force window to front of z-order."""
        try:
            # Method 1: Get current foreground window and thread
            current_fg = win32gui.GetForegroundWindow()
            current_thread = win32process.GetWindowThreadProcessId(current_fg)[0]
            target_thread = win32process.GetWindowThreadProcessId(self.hwnd)[0]

            # Method 2: Attach to foreground thread to bypass restrictions
            if current_thread != target_thread:
                win32process.AttachThreadInput(target_thread, current_thread, True)

            # Method 3: Temporarily make topmost, then remove topmost
            win32gui.SetWindowPos(
                self.hwnd,
                win32con.HWND_TOPMOST,
                x, y, width, height,
                win32con.SWP_SHOWWINDOW
            )

            # Method 4: Set as foreground window
            win32gui.SetForegroundWindow(self.hwnd)

            # Method 5: Remove topmost status but keep it at the top
            win32gui.SetWindowPos(
                self.hwnd,
                win32con.HWND_NOTOPMOST,
                x, y, width, height,
                win32con.SWP_SHOWWINDOW
            )

            # Method 6: Detach thread input
            if current_thread != target_thread:
                win32process.AttachThreadInput(target_thread, current_thread, False)

        except Exception:
            # Fallback: just use topmost toggle
            win32gui.SetWindowPos(
                self.hwnd,
                win32con.HWND_TOPMOST,
                x, y, width, height,
                win32con.SWP_SHOWWINDOW
            )
            win32gui.SetWindowPos(
                self.hwnd,
                win32con.HWND_NOTOPMOST,
                x, y, width, height,
                win32con.SWP_SHOWWINDOW
            )

    def __repr__(self):
        return f"<Window hwnd={self.hwnd} title='{self.title}' exe='{self.exe_path}' type={self.window_type.name}>"

# ---------------------------------------------------------
# 1) WindowManager
# ---------------------------------------------------------
class WindowManager:
    """
    Responsible for discovering and managing windows,
    but *only* includes those currently on-screen (non-minimized)
    that have actual non-trivial dimensions.
    """
    def __init__(self):
        self.windows = []

    def detect_windows(self, min_size=2):
        """
        Enumerate top-level windows including minimized ones.
        Uses GetWindowPlacement to get the restored size even for minimized windows.
        """
        self.windows = []

        def enum_callback(hwnd, _):
            if not self.is_alt_tab_style_window(hwnd):
                return True

            # Attempt process info
            _, pid = win32process.GetWindowThreadProcessId(hwnd)
            try:
                proc = psutil.Process(pid)
                exe_path = proc.exe().lower()
            except (psutil.Error, psutil.NoSuchProcess):
                return True

            # Use GetWindowPlacement to get the restored position/size
            # This works for both normal and minimized windows
            placement = win32gui.GetWindowPlacement(hwnd)
            left, top, right, bottom = placement[4]  # restored position
            width = right - left
            height = bottom - top

            # Skip windows that are too small even when restored
            if width < min_size or height < min_size:
                return True

            # Create our Window object
            self.windows.append(Window(hwnd, exe_path, (left, top, right, bottom)))
            return True

        win32gui.EnumWindows(enum_callback, None)

    def get_windows_by_process(self):
        """Group windows by process name, filtering out system processes."""
        # System processes that should not be tiled
        excluded_processes = {
            'applicationframehost.exe',  # Windows 10+ app container host
            'dllhost.exe',              # COM+ surrogate process
            'dwm.exe',                  # Desktop Window Manager
            'winlogon.exe',             # Windows logon process
            'csrss.exe',                # Client/Server Runtime Subsystem
            'wininit.exe',              # Windows initialization process
            'services.exe',             # Service Control Manager
            'lsass.exe',                # Local Security Authority
            'smss.exe',                 # Session Manager Subsystem
            'conhost.exe',              # Console host process
            'svchost.exe',              # Service host process
            'taskhostw.exe',            # Task host process (often system tasks)
            'sihost.exe',               # Shell infrastructure host
            'ctfmon.exe',               # Text services framework
            'searchui.exe',             # Windows search UI (often system)
            'startmenuexperiencehost.exe',  # Start menu host
            'shellexperiencehost.exe',  # Shell experience host
            'runtimebroker.exe',        # Runtime broker (often system)
            'backgroundtaskhost.exe',   # Background task host
        }

        groups = {}
        for w in self.windows:
            pname = w.process_name or "unknown"
            # Skip system processes
            if pname.lower() in excluded_processes:
                continue
            groups.setdefault(pname, []).append(w)
        return groups

    def get_windows_by_type(self):
        """Group windows by WindowType, filtering out system processes."""
        # System processes that should not be tiled (same list as in get_windows_by_process)
        excluded_processes = {
            'applicationframehost.exe',  # Windows 10+ app container host
            'dllhost.exe',              # COM+ surrogate process
            'dwm.exe',                  # Desktop Window Manager
            'winlogon.exe',             # Windows logon process
            'csrss.exe',                # Client/Server Runtime Subsystem
            'wininit.exe',              # Windows initialization process
            'services.exe',             # Service Control Manager
            'lsass.exe',                # Local Security Authority
            'smss.exe',                 # Session Manager Subsystem
            'conhost.exe',              # Console host process
            'svchost.exe',              # Service host process
            'taskhostw.exe',            # Task host process (often system tasks)
            'sihost.exe',               # Shell infrastructure host
            'ctfmon.exe',               # Text services framework
            'searchui.exe',             # Windows search UI (often system)
            'startmenuexperiencehost.exe',  # Start menu host
            'shellexperiencehost.exe',  # Shell experience host
            'runtimebroker.exe',        # Runtime broker (often system)
            'backgroundtaskhost.exe',   # Background task host
        }

        groups = {}
        for w in self.windows:
            pname = w.process_name or "unknown"
            # Skip system processes
            if pname.lower() in excluded_processes:
                continue
            groups.setdefault(w.window_type, []).append(w)
        return groups

    def filter_windows(self, predicate):
        """Return a list of windows matching the given predicate."""
        return [w for w in self.windows if predicate(w)]

    # ---------- Private Helpers ----------
    def is_alt_tab_style_window(self, hwnd):
        """
        True if this window is a normal user-facing top-level window
        that we typically see in Alt+Tab (including minimized windows).
        """
        if hwnd == SHELL_WINDOW:
            return False

        # Must be root ancestor
        if win32gui.GetAncestor(hwnd, win32con.GA_ROOT) != hwnd:
            return False

        # Get window styles
        style = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
        ex_style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)

        # Skip tool windows
        if ex_style & win32con.WS_EX_TOOLWINDOW:
            return False

        # Skip windows with owners (usually dialogs or child windows)
        if win32gui.GetWindow(hwnd, win32con.GW_OWNER) != 0:
            return False

        # Must have a title
        title = win32gui.GetWindowText(hwnd)
        if not title.strip():
            return False

        # Must have typical window characteristics for Alt+Tab windows
        # Either WS_CAPTION, WS_SYSMENU, or WS_EX_APPWINDOW
        has_caption = bool(style & win32con.WS_CAPTION)
        has_sysmenu = bool(style & win32con.WS_SYSMENU)
        has_appwindow = bool(ex_style & win32con.WS_EX_APPWINDOW)

        if not (has_caption or has_sysmenu or has_appwindow):
            return False

        # Include both visible windows and minimized windows
        # (minimized windows are not "visible" but should be included)
        is_visible = win32gui.IsWindowVisible(hwnd)
        is_minimized = win32gui.IsIconic(hwnd)

        if not (is_visible or is_minimized):
            return False

        return True

# ---------------------------------------------------------
# 2) MonitorManager
# ---------------------------------------------------------
class MonitorManager:
    """Responsible for discovering monitors and retrieving user-chosen monitors."""
    def __init__(self):
        self.monitors = []

    def detect_monitors(self):
        """Populate self.monitors with discovered Monitor objects."""
        self.monitors = []
        for (handle, _, info) in win32api.EnumDisplayMonitors(None, None):
            mon_info = win32api.GetMonitorInfo(handle)
            self.monitors.append(Monitor(handle, mon_info))

    def get_primary_monitor(self):
        """Return the primary monitor, or None if none found."""
        for m in self.monitors:
            if m.is_primary:
                return m
        return self.monitors[0] if self.monitors else None

    def get_monitor_by_index(self, index):
        """Return monitor by list index or None if out of range."""
        if 0 <= index < len(self.monitors):
            return self.monitors[index]
        return None

# ---------------------------------------------------------
# 3) LayoutManager
# ---------------------------------------------------------
class LayoutManager:
    """
    Responsible for applying different layout strategies (grid, custom, etc.).
    """
    def apply_grid_layout(self, windows, monitor, rows=2, cols=2, restore_minimized=True, bring_to_front=False):
        """Simple grid layout on the chosen monitor."""
        if not windows:
            print("No windows to tile.")
            return

        left, top, right, bottom = monitor.monitor_area
        total_width = right - left
        total_height = bottom - top

        n = min(len(windows), rows * cols)

        for i, w in enumerate(windows[:n]):
            row = i // cols
            col = i % cols

            x = left + int(col * (total_width / cols))
            y = top + int(row * (total_height / rows))
            wth = int(total_width / cols)
            hth = int(total_height / rows)

            w.move_and_resize(x, y, wth, hth, restore_minimized=restore_minimized, bring_to_front=bring_to_front)

        print(f"Tiled {n} windows in a {rows}x{cols} grid on {monitor.device}.")

    def apply_custom_layout(self, windows, monitor, layout_config):
        """Example placeholder for custom layouts."""
        pass

    def create_layout_preset(self, name, config):
        """Register a named preset for future usage."""
        pass

# ---------------------------------------------------------
# 4) UserInterface
# ---------------------------------------------------------
class UserInterface:
    """Handles all user-facing input/output prompts."""
    def show_monitor_selection(self, monitors):
        print("\nMonitors Detected:")
        for i, mon in enumerate(monitors):
            dims = mon.get_dimensions()
            primary_txt = " [PRIMARY]" if mon.is_primary else ""
            print(f"{i}) {mon.device} - {dims['width']}x{dims['height']}{primary_txt}")

    def prompt_monitor_index(self):
        return input("Select a monitor index [blank=choose primary]: ").strip()

    def show_grouped_windows(self, grouped):
        """Display grouped windows (key -> count)."""
        print("\nGroups Detected:")
        group_keys = sorted(grouped.keys(), key=lambda k: str(k))
        for idx, key in enumerate(group_keys):
            label = str(key)
            if isinstance(key, WindowType):
                label = key.name
            print(f"{idx}) {label} -> {len(grouped[key])} windows")
        return group_keys

    def get_user_choice_index(self):
        return input("\nWhich group do you want to tile? Enter index: ").strip()

    def get_layout_configuration(self, num_windows):
        """Prompt for rows, columns, and whether to restore minimized."""
        # Calculate dynamic defaults based on number of windows
        default_rows, default_cols = self._calculate_optimal_grid(num_windows)

        r = input(f"Number of rows [default={default_rows}]: ").strip()
        c = input(f"Number of columns [default={default_cols}]: ").strip()
        rows = int(r) if r.isdigit() else default_rows
        cols = int(c) if c.isdigit() else default_cols

        restore_prompt = input("Restore minimized windows? [Y/n]: ").strip().lower()
        restore_minimized = (restore_prompt != 'n')

        # Only ask about bringing to front if restore is enabled
        bring_to_front = False
        if restore_minimized:
            front_prompt = input("Force windows to foreground (aggressive activation)? [Y/n]: ").strip().lower()
            bring_to_front = (front_prompt != 'n')

        return rows, cols, restore_minimized, bring_to_front

    def _calculate_optimal_grid(self, num_windows):
        """Calculate optimal rows and columns for the given number of windows."""
        if num_windows <= 0:
            return 2, 2

        # For small numbers of windows, use simple layouts
        if num_windows == 1:
            return 1, 1
        elif num_windows == 2:
            return 1, 2
        elif num_windows == 3:
            return 1, 3
        elif num_windows == 4:
            return 2, 2
        elif num_windows <= 6:
            return 2, 3
        elif num_windows <= 8:
            return 2, 4
        elif num_windows == 9:
            return 3, 3
        elif num_windows <= 12:
            return 3, 4
        elif num_windows <= 15:
            return 3, 5
        elif num_windows == 16:
            return 4, 4
        else:
            # For larger numbers, try to create a roughly square grid
            # that can accommodate all windows
            sqrt_windows = math.sqrt(num_windows)
            rows = int(sqrt_windows)
            cols = math.ceil(num_windows / rows)

            # Ensure the grid can actually fit all windows
            while rows * cols < num_windows:
                if rows <= cols:
                    rows += 1
                else:
                    cols += 1

            return rows, cols

    def display_results(self, message):
        print(message)

# ---------------------------------------------------------
# 5) WindowTilerApp
# ---------------------------------------------------------
class WindowTilerApp:
    """
    Main application class that composes WindowManager, MonitorManager,
    LayoutManager, and UserInterface. Orchestrates the flow.
    """
    def __init__(self):
        self.windowManager = WindowManager()
        self.monitorManager = MonitorManager()
        self.layoutManager = LayoutManager()
        self.ui = UserInterface()
        self.running = True

    def run(self):
        """Main entry point of the application."""
        print("Gathering top-level windows (including minimized).")
        self.windowManager.detect_windows(min_size=2)
        windows = self.windowManager.windows

        if not windows:
            self.ui.display_results("No windows found!")
            return

        print(f"Total windows found: {len(windows)}")

        # Step: Choose grouping style
        print("\nChoose grouping style:")
        print("1) By process name (e.g. 'chrome.exe')")
        print("2) By window type (BROWSER, TERMINAL, EDITOR, etc.)")
        choice = input("Enter [1/2]: ").strip()

        if choice == "1":
            grouped = self.windowManager.get_windows_by_process()
        else:
            grouped = self.windowManager.get_windows_by_type()

        # Display group info
        group_keys = self.ui.show_grouped_windows(grouped)
        if not group_keys:
            self.ui.display_results("No groups found!")
            return

        # Step: Pick group
        chosen = self.ui.get_user_choice_index()
        try:
            chosen_idx = int(chosen)
            group_key = group_keys[chosen_idx]
        except:
            self.ui.display_results("Invalid choice, quitting.")
            return

        # Step: Layout config
        windows_to_tile = grouped[group_key]
        rows, cols, restore_minimized, bring_to_front = self.ui.get_layout_configuration(len(windows_to_tile))

        # Step: Monitor selection
        self.monitorManager.detect_monitors()
        monitors = self.monitorManager.monitors
        if not monitors:
            self.ui.display_results("No monitors found!")
            return

        self.ui.show_monitor_selection(monitors)
        index_str = self.ui.prompt_monitor_index()

        if not index_str:
            monitor = self.monitorManager.get_primary_monitor()
        else:
            try:
                idx = int(index_str)
                monitor = self.monitorManager.get_monitor_by_index(idx)
            except:
                self.ui.display_results("Invalid input, defaulting to first monitor.")
                monitor = monitors[0]

        if not monitor:
            monitor = monitors[0]

        # Step: Do the layout
        self.layoutManager.apply_grid_layout(
            windows_to_tile,
            monitor,
            rows,
            cols,
            restore_minimized=restore_minimized,
            bring_to_front=bring_to_front
        )

        self.ui.display_results("\nDone.")

    def exit(self):
        """Clean up resources if needed and exit."""
        self.running = False

# ---------------------------------------------------------
# 6) Entry Point
# ---------------------------------------------------------
def main():
    app = WindowTilerApp()
    app.run()

if __name__ == "__main__":
    main()
