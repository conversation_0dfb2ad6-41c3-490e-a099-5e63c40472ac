import win32api
import win32gui
import win32con

class Monitor:
    def __init__(self, monitor_handle, monitor_info):
        self.handle = monitor_handle
        self.flags = monitor_info['Flags']
        self.device = monitor_info['Device']
        self._set_areas(monitor_info['Monitor'], monitor_info['Work'])

    def _set_areas(self, monitor_area, work_area):
        self.monitor_area = monitor_area
        self.work_area = work_area
        self.monitor_area_dict = self._convert_to_dict(monitor_area)
        self.work_area_dict = self._convert_to_dict(work_area)

    def _convert_to_dict(self, area):
        return {
            'x': area[0],
            'y': area[1],
            'width': area[2] - area[0],
            'height': area[3] - area[1]
        }

    def update_monitor_area(self, new_area):
        self._set_areas(new_area, self.work_area)

    def update_work_area(self, new_area):
        self._set_areas(self.monitor_area, new_area)

    def is_primary(self):
        return self.flags == 1

    def get_dimensions(self):
        return {
            'width': self.monitor_area[2] - self.monitor_area[0],
            'height': self.monitor_area[3] - self.monitor_area[1]
        }

def get_all_monitors():
    monitors_info = {}
    for monitor in win32api.EnumDisplayMonitors(None, None):
        monitor_handle = monitor[0]
        monitor_info = win32api.GetMonitorInfo(monitor_handle)
        monitor_obj = Monitor(monitor_handle, monitor_info)
        monitors_info[monitor_handle] = monitor_obj

    return monitors_info

class Window:
    def __init__(self, hwnd):
        self.hwnd = hwnd
        self.update_dimensions()

    def update_dimensions(self):
        rect = win32gui.GetWindowRect(self.hwnd)
        self.dimensions = {
            'x': rect[0],
            'y': rect[1],
            'width': rect[2] - rect[0],
            'height': rect[3] - rect[1]
        }

    def tile(self, monitor: Monitor, position_ratio: tuple, size_ratio: tuple):
        monitor_area = monitor.monitor_area
        new_x = monitor_area[0] + int(position_ratio[0] * monitor_area[2])
        new_y = monitor_area[1] + int(position_ratio[1] * monitor_area[3])
        new_width = int(size_ratio[0] * monitor_area[2])
        new_height = int(size_ratio[1] * monitor_area[3])

        win32gui.MoveWindow(self.hwnd, new_x, new_y, new_width, new_height, True)

def get_all_windows():
    windows = {}
    def enum_windows(hwnd, result):
        if win32gui.IsWindowVisible(hwnd) and win32gui.GetWindowText(hwnd):
            windows[hwnd] = Window(hwnd)
    win32gui.EnumWindows(enum_windows, [])
    return windows

def tile_windows_on_primary_monitor(windows, tiling_patterns):
    primary_monitor = next((mon for mon in get_all_monitors().values() if mon.is_primary()), None)

    if not primary_monitor:
        print("No primary monitor found.")
        return

    for hwnd, window in windows.items():
        print(hwnd)
        if hwnd in tiling_patterns:
            print(hwnd)
            position_ratio, size_ratio = tiling_patterns[hwnd]
            window.tile(primary_monitor, position_ratio, size_ratio)

# Example usage
all_windows = get_all_windows()
# print(all_windows)
# Define tiling patterns for some windows
# Replace these with actual window handles
tiling_patterns = {
    "window_handle_1": ((0, 0), (0.5, 0.5)),  # Top-left quarter
    "window_handle_2": ((0.5, 0), (0.5, 0.5)),  # Top-right quarter
    "window_handle_3": ((0, 0.5), (0.5, 0.5)),  # Bottom-left quarter
    "window_handle_4": ((0.5, 0.5), (0.5, 0.5)),  # Bottom-right quarter
    # ... Add more patterns as needed ...
}

tile_windows_on_primary_monitor(all_windows, tiling_patterns)
print("xxxx")
