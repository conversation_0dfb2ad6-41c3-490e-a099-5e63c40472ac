# Active Context: Windows Window Tiler

## Current Work Focus

### Code Consolidation
The project is currently in a consolidation phase. Multiple implementations of similar functionality exist across different files and directories. Our primary focus is identifying the most complete and robust implementation and consolidating the code into a clean, unified structure.

Key files under consideration:
- **Core Files**: monitor.py, window.py, window_tiler.py (modular approach)
- **Consolidated Implementation**: completely_new_04.py (single-file approach)
- **Structured Implementation**: mess/window_manager/ (more complex approach with additional features)

### Implementation Analysis

#### Current Implementation Status
1. **Core Implementation** (root directory):
   - Clean separation of concerns (Monitor, Window, WindowTiler classes)
   - Basic window tiling functionality works
   - Limited window type detection

2. **Consolidated Version** (completely_new_04.py):
   - Combines all functionality in a single file
   - Enhanced window property access (is_enabled, is_minimized, get_text, get_class_name, get_style)
   - Comprehensive testing code

3. **Window Manager Implementation** (mess/window_manager/):
   - More detailed window type detection
   - Support for special folders and explorer windows
   - Enhanced window classification with WindowType enum
   - Process-to-window mapping with additional metadata

### Active Decisions and Considerations

#### Window Type Detection Strategy
A key focus area is implementing robust window filtering by type/process. We've identified three complementary approaches that should be combined for maximum flexibility and accuracy:

1. **Process-Based Identification**:
   ```python
   hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(hwnd)
   hwnd_process_handle = ctypes.windll.kernel32.OpenProcess(query_flags, False, hwnd_process_id)
   process_path = win32process.GetModuleFileNameEx(hwnd_process_handle, 0)
   process_name = os.path.basename(process_path)  # Extract executable name
   ```
   This approach allows identifying windows by their process name (e.g., "explorer.exe", "chrome.exe"), enabling application-level grouping.

2. **Class-Based Identification**:
   ```python
   window_class = win32gui.GetClassName(hwnd)
   if window_class == "CabinetWClass":  # Example for Explorer windows
       # Handle Explorer window
   ```
   This approach enables more granular control by identifying specific window types within an application (e.g., distinguishing between different types of Explorer windows).

3. **Special Case Handling**:
   ```python
   # For Explorer windows with special folders
   if window_class == 'CabinetWClass':
       shell_instance = shell_window_mapping.get(hwnd)
       if shell_instance:
           location_url = shell_instance.LocationURL
           # Process special folder information
   ```
   This approach handles edge cases and special windows that require specific detection methods.

The consolidated implementation will incorporate all three strategies within a unified `WindowType` classification system, enabling flexible filtering based on process name, window class, or special window types.

#### Code Architecture Decision
After evaluating the different implementation approaches, we've decided to:

1. **Maintain Separate Files for Core Components**:
   - `monitor.py`: Enhanced Monitor class with improved multi-monitor support
   - `window.py`: Enhanced Window class with robust type detection capabilities
   - `window_tiler.py`: WindowTiler class with process-based filtering support
   - `window_enums.py`: New file for enumeration types and constants
   - `window_utils.py`: New file for utility functions (shell windows, special folder handling)

2. **Add New Features from Various Implementations**:
   - Incorporate the enhanced window property methods from completely_new_04.py
   - Add the window type detection and classification from mess/window_manager/
   - Implement the special case handling for Explorer windows
   - Create a unified filtering mechanism that supports all detection strategies

3. **Enhance the API for Type-Based Tiling**:
   ```python
   # Example of the enhanced API
   def tile_windows_by_type(monitor, window_type=None, process_name=None, window_class=None, rows=2, columns=2):
       """Tile windows matching specified type criteria in a grid layout."""
       windows = get_windows_by_type(window_type, process_name, window_class)
       window_tiler = WindowTiler(monitors, windows)
       window_tiler.tile_windows(monitor, windows, rows, columns)
   ```

This architecture maintains the clean separation of concerns while incorporating the advanced features needed for type-based window tiling. It also establishes a foundation for future extensions like configuration support and additional tiling algorithms.

## Recent Changes

### Code Exploration and Analysis
- Analyzed existing implementations to understand functionality
- Identified key components and their relationships
- Mapped core features across different implementations

### Initial Consolidation Planning
- Established a memory bank to document project understanding
- Defined project goals and technical context
- Created a roadmap for code consolidation

## Next Steps

### Immediate Tasks
1. **Create Window Enums Implementation**:
   - Define WindowType enum for classification
   - Define other necessary enumerations for window states and operations
   - Implement in new `window_enums.py` file

2. **Enhance Window Class Implementation**:
   - Incorporate process-based identification
   - Add class-based identification
   - Support special case handling
   - Implement filtering capabilities
   - Add comprehensive window properties

3. **Create Window Utilities**:
   - Implement shell window instance retrieval
   - Add special folder handling
   - Create utility functions for window type detection

4. **Enhance WindowTiler for Type-Based Tiling**:
   - Add methods for filtering windows by type
   - Support tiling operations on filtered windows
   - Implement improved error handling

5. **Create Integration Tests**:
   - Test process-based window filtering
   - Test class-based window filtering
   - Test tiling operations with filtered windows

### Consolidation Steps
1. **Implement Enhanced Window Type Detection**:
   - Create `window_enums.py` with WindowType classifications
   - Update `window.py` with process and class-based identification
   - Add `window_utils.py` for special case handling

2. **Enhance Tiling API**:
   - Update `window_tiler.py` with type-based filtering support
   - Implement helper functions for common tiling operations

3. **Clean Up and Archive**:
   - Move existing implementation files to an 'archive' directory
   - Document the consolidated implementation
   - Create usage examples

## Important Patterns and Preferences

### Code Organization
- Clear separation of concerns between Monitors, Windows, and Tiling
- Factory methods for object creation
- Consistent error handling and validation

### Naming Conventions
- CamelCase for class names: `Monitor`, `Window`, `WindowTiler`
- snake_case for methods and functions: `get_all_windows()`, `resize_and_move()`
- Clear, descriptive method names that reflect their purpose

### API Design Principles
- Methods should have a single responsibility
- Window operations should be encapsulated within the Window class
- Tiling operations should be handled by the WindowTiler class
- Factory functions should handle system API complexity

## Project Insights and Learnings

### Win32 API Challenges
- Some windows don't respond to manipulation attempts due to security or implementation
- Window enumeration can return false positives or miss certain windows
- Screen coordinates require careful handling, especially with multiple monitors

### Window Management Complexity
- Window visibility, state (minimized, maximized), and Z-order affect manipulation
- Process-to-window mapping isn't always straightforward
- Special windows (like Task Manager) may require specific handling

### Efficiency Considerations
- Window enumeration and property retrieval should be optimized
- Large operations should consider batching window updates
- Calculations should prefer proportional positioning over absolute coordinates
