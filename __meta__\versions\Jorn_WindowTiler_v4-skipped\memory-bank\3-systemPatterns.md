# System Patterns

## Architecture Overview
The Jorn_WindowTiler follows a modular, component-based architecture with clear separation of concerns. The system is organized into distinct logical components that handle specific responsibilities.

### Core Components

```mermaid
flowchart TD
    WinEnum[Window Enumeration] --> WinFiltering[Window Filtering]
    WinFiltering --> Process[Process Information]
    WinFiltering --> WindowClassification[Window Classification]
    Process --> WindowObj[Window Objects]
    WindowClassification --> WindowObj
    WindowObj --> Grouping[Window Grouping]
    WindowObj --> Tiling[Window Tiling]
    MonitorEnum[Monitor Enumeration] --> MonitorObj[Monitor Objects]
    MonitorObj --> Tiling
    Grouping --> Tiling
```

## Key Technical Patterns

### 1. Object-Oriented Representation
- **Window Class**: Encapsulates window properties and behaviors
- **Monitor Class**: Represents physical display with dimensions and attributes
- **Wrapper Class**: (WrappedMonitor) Adds UI-friendly representation to core objects

### 2. Strategy Pattern for Classification
- The system uses a classification strategy to categorize windows by type
- Implemented through the `WindowType` enumeration and `_classify` method
- Allows for different classification approaches without changing core code

### 3. Factory Methods
- `get_all_windows()`: Creates window objects through enumeration
- `get_all_monitors()`: Creates monitor objects through system API calls

### 4. Composition Over Inheritance
- Windows and Monitors are independent classes with clear responsibilities
- Relationships expressed through composition rather than inheritance hierarchies
- `Window` objects contain process information rather than inheriting from a Process class

### 5. Command Pattern for Window Operations
- Window manipulation operations encapsulated in methods
- `move_and_resize()` implements the command pattern for window operations

### 6. Filtering Chain
- Multiple filtering steps applied in sequence:
  1. Style-based filtering (Alt+Tab visibility)
  2. Process-based filtering
  3. Size-based filtering

### 7. User Interface Pattern
- Command-line interface with progressive disclosure
- Information presented step-by-step based on user choices

## Data Flow

```mermaid
flowchart LR
    Input[User Input] --> Enum[Window Enumeration]
    Enum --> Filter[Apply Filters]
    Filter --> Group[Group Windows]
    Group --> Present[Present Groups]
    Present --> Select[User Selection]
    Select --> Config[Configure Grid]
    Config --> Tile[Tile Windows]
```

## Key Decision Records

### KDR-1: Style-Based Window Filtering
- **Decision**: Use Windows API style flags to identify "Alt+Tab style" windows
- **Rationale**: Matches user expectations by only showing windows visible in Alt+Tab
- **Alternative Considered**: Simple top-level window enumeration (rejected due to too many system windows)

### KDR-2: Process Information via psutil
- **Decision**: Use psutil library to retrieve process information
- **Rationale**: Provides cross-version compatibility and richer process information
- **Alternative Considered**: Direct Windows API calls (rejected due to complexity)

### KDR-3: Classification by Window Type
- **Decision**: Implement a window classification system based on process name and class
- **Rationale**: Allows for logical grouping that matches user mental models
- **Alternative Considered**: No classification (rejected as less user-friendly)

### KDR-4: Monitor Handling
- **Decision**: Support explicit monitor selection with primary monitor default
- **Rationale**: Provides flexibility for multi-monitor setups while maintaining simplicity
- **Alternative Considered**: Always use primary monitor (rejected as too limiting)
