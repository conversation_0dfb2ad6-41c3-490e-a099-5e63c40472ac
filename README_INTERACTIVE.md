# Window Tiler - Prompt Edition

## 🚀 New Prompt Mode Features

The Window Tiler has been enhanced with **persistent prompt sessions** that maintain uninterrupted operation until explicitly terminated by the user.

### ✨ Key Improvements

- **🔄 Continuous Loop**: The application runs indefinitely until you choose to exit
- **🛡️ Robust Error Handling**: Recovers from errors and returns to the main menu
- **⌨️ Ctrl+C Protection**: Gracefully handles interruption attempts
- **📋 Main Menu System**: Easy-to-use menu interface for all operations
- **❓ Built-in Help**: Comprehensive help system accessible anytime
- **✅ Confirmation Prompts**: User-friendly confirmations for all actions

## 🎯 Usage

### Prompt Mode (Default)
```bash
# Start persistent prompt session
python src/window_tiler.py

# Or explicitly specify prompt mode
python src/window_tiler.py --prompt
```

### Legacy Mode (One-time execution)
```bash
# Run once and exit (original behavior)
python src/window_tiler.py --once
```

## 📋 Prompt Menu Options

When you run the application, you'll see a main menu with these options:

1. **Tile windows** - Arrange windows in customizable grids
2. **List all windows** - View all detected windows with details
3. **List monitors** - Display information about available monitors
4. **Show help** - Access comprehensive help documentation
5. **Exit** - Safely terminate the session

## 🔧 Session Flow

1. **Start**: Application launches and displays welcome message
2. **Menu Loop**: Main menu appears after each operation
3. **Operation**: Choose and execute desired function
4. **Continue**: Confirm whether to return to menu or exit
5. **Repeat**: Process continues until explicit exit

## 🛡️ Error Handling

The interactive mode includes robust error handling:

- **Graceful Recovery**: Errors don't terminate the session
- **User Feedback**: Clear error messages with guidance
- **Menu Return**: Always returns to main menu after errors
- **Interrupt Protection**: Ctrl+C is caught and handled gracefully

## 🎨 Features

### Window Management
- Group windows by process name or type
- Customizable grid layouts (rows × columns)
- Multi-monitor support with detailed monitor information
- Restore minimized windows option
- Taskbar-aware positioning
- Aggressive window activation option

### User Experience
- Rich console formatting with colors and symbols
- Dynamic grid size suggestions based on window count
- Detailed window and monitor listings
- Comprehensive help system
- Confirmation prompts for destructive actions

## 🔄 Session Persistence

The prompt mode ensures:

- **No Automatic Exits**: Application only exits when explicitly requested
- **Continuous Availability**: All functions remain accessible throughout the session
- **State Preservation**: Window and monitor detection refreshed for each operation
- **User Control**: Complete control over when to terminate the session

## 🚪 Exit Methods

You can exit the application in several ways:

1. **Menu Option 5**: Select "Exit" from the main menu
2. **Confirmation**: Confirm exit when prompted after operations
3. **Force Exit**: Use Ctrl+C multiple times (not recommended)

## 🧪 Testing

Run the test script to verify functionality:

```bash
python test_interactive.py
```

## 📝 Example Session

```
🪟 Welcome to Window Tiler Prompt Mode!
This session will continue until you explicitly choose to exit.

============================================================
           WINDOW TILER - PROMPT MODE
============================================================

Available Commands:
1) Tile windows
2) List all windows
3) List monitors
4) Show help
5) Exit
------------------------------
Select an option [1-5] (1): 1

Finding windows...
Total windows found: 8

Choose grouping style:
1) Tile all 8 windows
2) By process name (e.g. 'chrome.exe')
3) By window type (BROWSER, TERMINAL, EDITOR, etc.)
Enter [1/2/3] (2): 2

[... tiling process continues ...]

✅ Tiling completed successfully!

----------------------------------------
Return to main menu? [Y/n]: y

[... menu appears again for next operation ...]
```

## 🎯 Benefits

- **Productivity**: No need to restart the application for multiple operations
- **Efficiency**: Quick access to all functions without command-line arguments
- **User-Friendly**: Intuitive menu system with clear options
- **Reliable**: Robust error handling prevents session termination
- **Flexible**: Choose between prompt and legacy modes as needed

The prompt mode transforms Window Tiler from a one-shot utility into a persistent desktop management tool that stays available whenever you need it!
