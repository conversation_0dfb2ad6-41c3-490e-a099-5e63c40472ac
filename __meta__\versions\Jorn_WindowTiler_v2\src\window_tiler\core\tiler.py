"""
Tiler Module for Window Tiler

This module provides functionality for arranging windows in various layouts
on monitors. It includes a Tiler class that handles the arrangement calculations
and operations.
"""

from window_tiler.core.window import Window, get_windows_by_type
from window_tiler.core.types import WindowType


class Tiler:
    """
    Handles the arrangement of windows in various layouts.
    
    Attributes:
        monitors: Dictionary of monitors to use for tiling
    """
    
    def __init__(self, monitors):
        """
        Initialize a Tiler object.
        
        Args:
            monitors: Dictionary of monitors from get_all_monitors()
        """
        self.monitors = monitors
        
    def tile_grid(self, monitor, windows, rows=2, columns=2, column_ratios=None, row_ratios=None):
        """
        Arrange windows in a grid layout on a specific monitor.
        
        Args:
            monitor: Monitor to tile windows on
            windows: List of Window objects to arrange
            rows: Number of rows in the grid
            columns: Number of columns in the grid
            column_ratios: Optional list of column width ratios (must sum to 1.0)
            row_ratios: Optional list of row height ratios (must sum to 1.0)
        """
        if rows <= 0 or columns <= 0:
            raise ValueError("Rows and columns must be greater than 0")
            
        # Validate ratio length
        if column_ratios and len(column_ratios) != columns:
            raise ValueError(f"Column ratios length ({len(column_ratios)}) must match columns ({columns})")
            
        if row_ratios and len(row_ratios) != rows:
            raise ValueError(f"Row ratios length ({len(row_ratios)}) must match rows ({rows})")
            
        # Validate ratios sum to approximately 1.0
        if column_ratios and abs(sum(column_ratios) - 1.0) > 0.00001:
            raise ValueError(f"Column ratios must sum to 1.0 (got {sum(column_ratios)})")
            
        if row_ratios and abs(sum(row_ratios) - 1.0) > 0.00001:
            raise ValueError(f"Row ratios must sum to 1.0 (got {sum(row_ratios)})")
            
        # Default ratios are equal distribution
        default_column_width = 1.0 / columns
        default_row_height = 1.0 / rows
        
        # Position each window
        for i, window in enumerate(windows):
            # Skip if we've run out of grid cells
            if i >= rows * columns:
                break
                
            # Calculate grid position
            col = i % columns
            row = i // columns
            
            # Calculate position and size
            x_ratio = sum(column_ratios[:col]) if column_ratios else col * default_column_width
            y_ratio = sum(row_ratios[:row]) if row_ratios else row * default_row_height
            
            width_ratio = column_ratios[col] if column_ratios else default_column_width
            height_ratio = row_ratios[row] if row_ratios else default_row_height
            
            # Tile the window
            window.tile(monitor, (x_ratio, y_ratio), (width_ratio, height_ratio))
            
    def tile_by_type(self, monitor, window_type=None, process_name=None, window_class=None, 
                    rows=2, columns=2, column_ratios=None, row_ratios=None):
        """
        Filter windows by type and arrange them in a grid layout.
        
        Args:
            monitor: Monitor to tile windows on
            window_type: Optional WindowType to filter by
            process_name: Optional process name to filter by
            window_class: Optional window class to filter by
            rows: Number of rows in the grid
            columns: Number of columns in the grid
            column_ratios: Optional list of column width ratios
            row_ratios: Optional list of row height ratios
        """
        # Get windows filtered by criteria
        windows = get_windows_by_type(window_type, process_name, window_class)
        
        # Arrange the filtered windows in a grid
        self.tile_grid(monitor, windows, rows, columns, column_ratios, row_ratios)
        
    def maximize_all(self, monitor, windows):
        """
        Maximize all windows to fill the monitor.
        
        Args:
            monitor: Monitor to maximize windows on
            windows: List of Window objects
        """
        for window in windows:
            window.maximize_within_monitor(monitor)
            
    def cascade(self, monitor, windows, offset_x=30, offset_y=30):
        """
        Arrange windows in a cascading layout.
        
        Args:
            monitor: Monitor to arrange windows on
            windows: List of Window objects
            offset_x: Horizontal offset between windows
            offset_y: Vertical offset between windows
        """
        monitor_dims = monitor.get_dimensions()
        base_width = int(monitor_dims['width'] * 0.8)
        base_height = int(monitor_dims['height'] * 0.8)
        
        # Start position
        start_x = monitor.monitor_area[0]
        start_y = monitor.monitor_area[1]
        
        # Position each window with offset
        for i, window in enumerate(windows):
            x = start_x + (i * offset_x)
            y = start_y + (i * offset_y)
            
            # Ensure window stays within monitor
            if x + base_width > monitor.monitor_area[2]:
                x = start_x
                
            if y + base_height > monitor.monitor_area[3]:
                y = start_y
                
            # Resize and move window
            window.resize_and_move(x, y, base_width, base_height)
            
    def split(self, monitor, primary_window, secondary_windows, primary_ratio=0.6, 
             orientation='vertical'):
        """
        Split screen between a primary window and several secondary windows.
        
        Args:
            monitor: Monitor to arrange windows on
            primary_window: Main Window object
            secondary_windows: List of secondary Window objects
            primary_ratio: Ratio of screen space for primary window (0.0-1.0)
            orientation: 'vertical' (side by side) or 'horizontal' (stacked)
        """
        if orientation not in ['vertical', 'horizontal']:
            raise ValueError("Orientation must be 'vertical' or 'horizontal'")
            
        secondary_count = len(secondary_windows)
        if secondary_count == 0:
            # Only primary window, maximize it
            primary_window.maximize_within_monitor(monitor)
            return
            
        if orientation == 'vertical':
            # Primary window on the left
            primary_window.tile(monitor, (0, 0), (primary_ratio, 1.0))
            
            # Secondary windows stacked on the right
            secondary_width = 1.0 - primary_ratio
            secondary_height = 1.0 / secondary_count
            
            for i, window in enumerate(secondary_windows):
                y_position = i * secondary_height
                window.tile(monitor, 
                           (primary_ratio, y_position), 
                           (secondary_width, secondary_height))
        else:
            # Primary window on top
            primary_window.tile(monitor, (0, 0), (1.0, primary_ratio))
            
            # Secondary windows side by side at bottom
            secondary_width = 1.0 / secondary_count
            secondary_height = 1.0 - primary_ratio
            
            for i, window in enumerate(secondary_windows):
                x_position = i * secondary_width
                window.tile(monitor, 
                           (x_position, primary_ratio), 
                           (secondary_width, secondary_height))


def tile_windows(monitor, windows, rows=2, columns=2, column_ratios=None, row_ratios=None):
    """
    Simple function to tile windows in a grid layout.
    
    Args:
        monitor: Monitor to tile windows on
        windows: List of Window objects to arrange
        rows: Number of rows in the grid
        columns: Number of columns in the grid
        column_ratios: Optional list of column width ratios
        row_ratios: Optional list of row height ratios
    """
    tiler = Tiler({monitor.handle: monitor})
    tiler.tile_grid(monitor, windows, rows, columns, column_ratios, row_ratios)
