# Default
import os
import sys
import re
import time
import random
import ctypes
import urllib.parse
import json
import math
from enum import Enum

# Rich
from rich.console import Console
from rich.table import Table

# Import pywin32 modules
import win32com.client
import win32com.shell.shellcon as shellcon


import win32gui
import win32api
import win32process

from win32api import (
    MonitorFromWindow,
    GetMonitorInfo,
    EnumDisplayMonitors,
)

from win32gui import (
    IsWindowVisible,
    IsWindowEnabled,
    IsIconic,
    GetWindowText,
    GetClassName,
    GetWindowLong,
    EnumWindows,
    MoveWindow,
)
from win32con import (
    MONITOR_DEFAULTTONEAREST,
    GWL_STYLE,
    GWL_EXSTYLE,
)
# {0: {'Monitor': (0, 0, 3072, 1728), 'Work': (0, 0, 3072, 1688), 'Flags': 1, 'Device': '\\\\.\\DISPLAY1'}, 1: {'Monitor': (-1920, 0, 0, 1080), 'Work': (-1920, 0, 0, 1040), 'Flags': 0, 'Device': '\\\\.\\DISPLAY2'}}

import win32api

class Monitor:
    def __init__(self, monitor_handle, monitor_info):
        self.handle = monitor_handle
        self.flags = monitor_info['Flags']
        self.device = monitor_info['Device']
        self._set_areas(monitor_info['Monitor'], monitor_info['Work'])

    def _set_areas(self, monitor_area, work_area):
        self.monitor_area = monitor_area
        self.work_area = work_area
        self.monitor_area_dict = self._convert_to_dict(monitor_area)
        self.work_area_dict = self._convert_to_dict(work_area)

    def _convert_to_dict(self, area):
        return {
            'x': area[0],
            'y': area[1],
            'width': area[2] - area[0],
            'height': area[3] - area[1]
        }

    def update_monitor_area(self, new_area):
        self._set_areas(new_area, self.work_area)

    def update_work_area(self, new_area):
        self._set_areas(self.monitor_area, new_area)

    def is_primary(self):
        return self.flags == 1

    # Add more methods as needed

def get_all_monitors():
    monitors_info = {}
    for idx, monitor in enumerate(win32api.EnumDisplayMonitors(None, None)):
        monitor_handle = monitor[0]
        monitor_info = win32api.GetMonitorInfo(monitor_handle)
        monitor_obj = Monitor(monitor_handle, monitor_info)
        print(dir(monitor_obj))
        monitors_info[monitor_handle] = monitor_obj

    return monitors_info

# Example usage
all_monitors = get_all_monitors()
for handle, monitor in all_monitors.items():
    # print(dir(handle))
    print(f"Monitor {handle}: Area - {monitor.monitor_area_dict}, Primary - {monitor.is_primary()}")



time.sleep(99999)

import win32api

class Monitor:
    def __init__(self, monitor_handle, monitor_info):
        self.handle = monitor_handle
        self.monitor_area = monitor_info['Monitor']
        self.work_area = monitor_info['Work']
        self.flags = monitor_info['Flags']
        self.device = monitor_info['Device']

    def get_area_as_dict(self):
        return {
            'x': self.monitor_area[0],
            'y': self.monitor_area[1],
            'width': self.monitor_area[2] - self.monitor_area[0],
            'height': self.monitor_area[3] - self.monitor_area[1]
        }

    def is_primary(self):
        return self.flags == 1

    # Add more methods as needed

def get_all_monitors():
    monitors_info = {}
    for idx, monitor in enumerate(win32api.EnumDisplayMonitors(None, None)):
        monitor_handle = monitor[0]
        monitor_info = win32api.GetMonitorInfo(monitor_handle)
        monitor_obj = Monitor(monitor_handle, monitor_info)
        monitors_info[monitor_handle] = monitor_obj

    return monitors_info

# Example usage
all_monitors = get_all_monitors()
for handle, monitor in all_monitors.items():
    print(f"Monitor {handle}: Area - {monitor.get_area_as_dict()}, Primary - {monitor.is_primary()}")




time.sleep(99999)
def get_all_monitors():
    monitors = {}

    for idx, monitor in enumerate(win32api.EnumDisplayMonitors(None, None)):
        monitor_handle = monitor[0]
        monitor_info = win32api.GetMonitorInfo(monitor_handle)
        print(monitor_info.keys())
        # (left, top, right, bottom)
        print(monitor_info['Monitor'])
        print(monitor_info['Work'])
        print(f'width: {monitor_info["Work"][2]}')
        print(f'height: {monitor_info["Work"][3]}')

        time.sleep(99999)


        monitors[monitor_handle] = {
            'screen_width'   : monitor_info['Work'][2], #
            'screen_height'   : monitor_info['Work'][3], #
            'enabled'   : win32gui.IsWindowEnabled(hwnd),
            'visible'   : win32gui.IsWindowVisible(hwnd),
            'minimized' : win32gui.IsIconic(hwnd),
            'title'     : win32gui.GetWindowText(hwnd),
            'class'     : win32gui.GetClassName(hwnd),
            'style'     : win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE),
            'ex_style'  : win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE),
        }
        monitors[handle] = info

    return monitors

a = get_all_monitors()
print(a)

print('\n')
print('\n')
print(win32api.EnumDisplayMonitors(hdc, lprcClip))


time.sleep(99999)

def get_all_monitors():
    monitors = {}
    for idx, monitor in enumerate(win32api.EnumDisplayMonitors(None, None)):
        monitor_handle = monitor[0]
        monitor_info = win32api.GetMonitorInfo(monitor_handle)
        monitors[monitor_handle] = {
            # s
        }

    return monitors_info


def get_all_windows():
    windows = {}
    def enum_windows(hwnd, result):
        windows[hwnd] = {
            'monitor'   : get_hwnd_monitor(hwnd),
            'enabled'   : win32gui.IsWindowEnabled(hwnd),
            'visible'   : win32gui.IsWindowVisible(hwnd),
            'minimized' : win32gui.IsIconic(hwnd),
            'title'     : win32gui.GetWindowText(hwnd),
            'class'     : win32gui.GetClassName(hwnd),
            'style'     : win32gui.GetWindowLong(hwnd, GWL_STYLE),
            'ex_style'  : win32gui.GetWindowLong(hwnd, GWL_EXSTYLE),
        }
    win32gui.EnumWindows(enum_windows, [])
    return windows
get_all_monitors()
time.sleep(99999)




def get_hwnd_monitor(hwnd):
    monitor_handle = MonitorFromWindow(hwnd, MONITOR_DEFAULTTONEAREST)
    monitor_info = GetMonitorInfo(monitor_handle)
    print(monitor_info)
    monitor_device = (monitor_info["Device"])
    monitor_string = "".join(filter(str.isdigit, monitor_device))
    # monitor_index = int(monitor_string) if monitor_string else None
    # return monitor_index
    return monitor_string


def get_all_windows():
    windows = {}
    def enum_windows(hwnd, result):
        windows[hwnd] = {
            'monitor'   : get_hwnd_monitor(hwnd),
            'enabled'   : win32gui.IsWindowEnabled(hwnd),
            'visible'   : win32gui.IsWindowVisible(hwnd),
            'minimized' : win32gui.IsIconic(hwnd),
            'title'     : win32gui.GetWindowText(hwnd),
            'class'     : win32gui.GetClassName(hwnd),
            'style'     : win32gui.GetWindowLong(hwnd, GWL_STYLE),
            'ex_style'  : win32gui.GetWindowLong(hwnd, GWL_EXSTYLE),
        }
    win32gui.EnumWindows(enum_windows, [])
    return windows
get_all_windows()
time.sleep(99999)

def filter_windows(windows, is_normal=True, is_enabled=True, is_visible=True, has_title=True, has_class=True):
    filtered = {}
    for hwnd, attributes in windows.items():
        special_types = ("tooltips_class32", "Shell_TrayWnd")
        if is_normal and attributes['class'].startswith(special_types):
            continue
        if is_enabled and not attributes['enabled']:
            continue
        if is_visible and not attributes['visible']:
            continue
        if has_title and not attributes['title']:
            continue
        if has_class and not attributes['class']:
            continue

        filtered[hwnd] = attributes

    return filtered

# def filter_windows(windows, is_visible=True, is_minimizedminimized=True, window_text=None, window_class=None, ignore_case=True):
#     """Filter a list of windows based on the provided criteria."""
#     filtered = {}
#     for hwnd, title, class_name in windows:
#         if is_visible and not IsWindowVisible(hwnd):
#             continue
#         if title == "":
#             continue
#         if window_class is not None:
#             if ignore_case and class_name.lower() != window_class.lower():
#                 continue
#             elif not ignore_case and class_name != window_class:
#                 continue
#         if window_text is not None:
#             if ignore_case and not re.search(window_text, title, re.IGNORECASE):
#                 continue
#             elif not ignore_case and not re.search(window_text, title):
#                 continue
#         filtered[hwnd] = attributes
#     return filtered




def sort_windows(windows, sort_criteria):
    sorted_keys = sorted(
        windows.keys(),
        key=lambda x: tuple(windows[x].get(field, "") for field, order in sort_criteria),
        reverse=any(order == "desc" for field, order in sort_criteria),
    )
    return {k: windows[k] for k in sorted_keys}

def print_window_data(windows):
    table = Table(title="Windows Information")

    table.add_column("HWND", justify="right")
    table.add_column("Monitor")
    table.add_column("Title")
    table.add_column("Visible")
    table.add_column("Minimized")
    table.add_column("Class")
    table.add_column("Style", justify="right")
    table.add_column("Extended Style", justify="right")

    for hwnd, info in windows.items():
        table.add_row(
            str(hwnd),
            info['monitor'],
            info['title'],
            str(info['visible']),
            str(info['minimized']),
            info['class'],
            str(info['style']),
            str(info['ex_style'])
        )

    console = Console()
    console.print(table)

# Example usage
# window_manager = WindowManager()
all_windows = get_all_windows()
filtered_windows = filter_windows(all_windows)
sorted_windows = sort_windows(filtered_windows, [('monitor', 'desc'), ('visible', 'desc'), ('class', 'desc'), ('title', 'desc')])
print_window_data(sorted_windows)
# filtered_windows = window_manager.filter_windows()
# window_manager.print_window_data(sorted_windows)


# print(get_all_windows())
