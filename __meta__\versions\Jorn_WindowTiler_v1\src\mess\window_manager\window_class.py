import win32gui
import win32process
import ctypes

from .window_utils import get_shell_windows_instance, get_explorer_windows
from .window_enums import WindowType

# The 'Window' class represents a window object and holds its properties and methods.
# Each instance stores data on one single window
class Window:
    # Generate a Window object (from 'HWND')
    def __init__(self, hwnd, shell_window_mapping, special_folders_mapping):
        # Generate common window data.
        self.hwnd = hwnd
        self.hwnd_enabled = win32gui.IsWindowEnabled(hwnd)
        self.hwnd_visible = win32gui.IsWindowVisible(hwnd)
        self.hwnd_title = win32gui.GetWindowText(hwnd)
        self.hwnd_class = win32gui.GetClassName(hwnd)
        self._hwnd_placement = win32gui.GetWindowPlacement(hwnd)
        self._hwnd_rect = win32gui.GetWindowRect(hwnd)
        self.hwnd_controls_state = self._hwnd_placement[1]
        self.hwnd_position = (self._hwnd_rect[0], self._hwnd_rect[1])
        self.hwnd_size = ((self._hwnd_rect[2]-self._hwnd_rect[0]), (self._hwnd_rect[3]-self._hwnd_rect[1]))

        # Prepare instance variables for the window type and create-command.
        self.hwnd_type = None
        self.hwnd_create_cmd = None

        # Prepare instance variables for (explorer) folder view options.
        self.folder_icon_size = None
        self.folder_view_mode = None
        self.folder_sort_column = None
        self.folder_group_by = None
        # Prepare instance variable for file selection in folder.
        self.folder_selected_files = None
        self.folder_all_files = None
        self.folder_focused_file = None

        # Prepare instance variables for the window process.
        self.hwnd_process = None
        self.hwnd_process_id = None


        # Retrieve the process handle of the window
        hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(self.hwnd)
        hwnd_process_query = (win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ)
        hwnd_process_handle = ctypes.windll.kernel32.OpenProcess(hwnd_process_query, False, hwnd_process_id)
        # If a process handle was optained
        if hwnd_process_handle:
            # Update instance variables with the executable path and id
            self.hwnd_process = win32process.GetModuleFileNameEx(hwnd_process_handle, 0)
            self.hwnd_process_id = hwnd_process_id
            # Update 'type' with the identifier 'UNSPECIFIED'.
            self.hwnd_type = WindowType.UNSPECIFIED
        # Else if no process was retrieved, set 'type' to 'UNLINKED'.
        elif not self.hwnd_type:
            self.hwnd_type = WindowType.UNLINKED


        # If this is a Windows File-Explorer Window (typically a folder/directory)
        if self.hwnd_class == 'CabinetWClass':
            # Retrieve the folder path through its shell instance
            hwnd_shell_instance = shell_window_mapping[self.hwnd]
            hwnd_shell_path = hwnd_shell_instance.LocationURL

            # If it's a 'SPECIAL_FOLDER' (explorer window without retrievable path):
            # - Update the instance variable 'type' with the identifier 'SPECIAL_FOLDER'.
            # - Check if the path refers to a GUID (global unique identification number).
            # - Transform the path into a cmd-command (using 'Shell:{GUID}' or 'File:/URI'),
            #   this is to make the path executable (in that it creates the actual window).
            # - Update the instance variable 'create_cmd' with the modified path-command.
            if self.hwnd_title in special_folders_mapping:
                self.hwnd_type = WindowType.SPECIAL_FOLDER
                folder_path = special_folders_mapping[self.hwnd_title]
                folder_path_guid_match = bool(re.search(r'::{[\w-]*}', folder_path))
                folder_path_is_guid = folder_path_guid_match if not os.path.exists(folder_path) else False
                command_prefix = 'Shell:' if folder_path_is_guid else 'File:/'
                create_command = os.path.normpath(urllib.parse.unquote(f'{command_prefix}{folder_path}'))
                self.hwnd_create_cmd = create_command

            # Else it's a 'NORMAL_FOLDER' (explorer window with a retrievable path):
            # - Update the instance variable 'type' with the identifier 'NORMAL_FOLDER'.
            # - Update the instance variable 'create_cmd' with the path (URI).
            # - Update instance variables for folder view options.
            # - Update instance variables for files in folder.
            elif (hwnd_shell_path != ''):
                self.hwnd_type = WindowType.NORMAL_FOLDER
                self.hwnd_create_cmd = os.path.normpath(urllib.parse.unquote(hwnd_shell_path))
                folder_obj = hwnd_shell_instance.Document
                self.folder_icon_size = folder_obj.IconSize
                self.folder_view_mode = folder_obj.CurrentViewMode
                self.folder_sort_column = folder_obj.SortColumns
                self.folder_group_by = folder_obj.GroupBy
                self.folder_selected_files = [file.Name for file in folder_obj.SelectedItems()]
                self.folder_all_files = [file.Name for file in folder_obj.Folder.Items()]
                self.folder_focused_file = folder_obj.FocusedItem.Name if folder_obj.FocusedItem else None

        # If a title and class was found but no type has been retrieved (no associated window process):
        # - Set instance variable 'type' to 'UNLINKED'.
        if self.hwnd_title and self.hwnd_class and not self.hwnd_type:
            self.hwnd_type = WindowType.UNLINKED
