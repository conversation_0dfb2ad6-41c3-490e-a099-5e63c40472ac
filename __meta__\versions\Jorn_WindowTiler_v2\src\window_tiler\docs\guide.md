# Window Tiler - Complete Guide

This guide provides a comprehensive overview of the Window Tiler project, its architecture, and how to use it effectively.

## Overview

Window Tiler is a utility for dynamically arranging windows on Windows operating systems based on their type (process name, window class), enabling efficient workspace organization across multiple monitors. It is designed with simplicity, elegance, and clarity in mind, providing a powerful yet minimal API for window management.

## Core Concepts

### Window Type Detection

One of the key features of Window Tiler is its ability to detect and categorize windows based on various criteria:

1. **Process-Based Detection**
   - Windows are categorized based on their associated process (executable)
   - Examples: chrome.exe, explorer.exe, cmd.exe

2. **Class-Based Detection**
   - Windows are categorized based on their window class
   - Examples: CabinetWClass (Explorer), Chrome_WidgetWin_1 (Chrome)

3. **Special Case Handling**
   - Special folders and specific window types get additional treatment
   - Example: Explorer windows for "This PC" vs regular folder windows

The system combines these approaches to provide accurate and flexible window filtering.

### Monitor Representation

Monitors are represented as objects with:
- Boundaries and dimensions
- Work areas (excluding taskbar)
- Primary/secondary status
- Device information

### Window Manipulation

Windows can be:
- Tiled in grid layouts with customizable dimensions
- Maximized within monitor boundaries
- Centered on monitors
- Arranged in cascading layouts
- Toggled between visible and hidden states
- Focused or brought to front

### Tiling Strategies

The system supports multiple tiling strategies:
- Grid layout with configurable rows and columns
- Grid layout with custom size ratios
- Split layout (primary + secondary windows)
- Cascade layout

## Architecture

### Core Components

1. **Monitor Module** (`core/monitor.py`)
   - Detects and represents physical monitors
   - Provides monitor information and coordinate translation

2. **Window Module** (`core/window.py`)
   - Detects and represents application windows
   - Provides window manipulation methods
   - Handles window property access

3. **Tiler Module** (`core/tiler.py`)
   - Implements window arrangement algorithms
   - Provides layout strategies

4. **Types Module** (`core/types.py`)
   - Defines enumeration types for window classification
   - Used for type-safe window filtering

5. **Utils Module** (`utils.py`)
   - Provides utility functions for shell windows and processes
   - Implements window type detection logic

6. **Main Module** (`main.py`)
   - Provides command-line interface
   - Implements high-level commands

### Design Principles

1. **Simplicity**: Each component has a clear, focused purpose
2. **Clarity**: Intuitive API design with descriptive names
3. **Modularity**: Components are designed to work together but remain independent
4. **Flexibility**: Support for multiple arrangement strategies and window types
5. **Extensibility**: Easy to add new window types or tiling strategies

## Usage Guide

### Installation and Setup

1. **Requirements**
   - Python 3.7 or higher
   - pywin32 package

2. **Installation Steps**
   ```
   pip install pywin32
   ```

3. **Running the Application**
   ```
   python -m window_tiler.main [command] [options]
   ```

### Command-Line Usage

The application provides a command-line interface for common operations:

#### Basic Commands

- **List all windows**:
  ```
  python -m window_tiler.main list
  ```

- **List windows with types**:
  ```
  python -m window_tiler.main list --types
  ```

- **List monitors**:
  ```
  python -m window_tiler.main monitors
  ```

#### Tiling Commands

- **Tile by process name**:
  ```
  python -m window_tiler.main process chrome.exe --rows 2 --columns 2
  ```

- **Tile by window class**:
  ```
  python -m window_tiler.main class CabinetWClass --rows 1 --columns 2
  ```

- **Tile all browsers**:
  ```
  python -m window_tiler.main browsers --rows 2 --columns 2
  ```

- **Tile all Explorer windows**:
  ```
  python -m window_tiler.main explorer --rows 2 --columns 2
  ```

- **Tile all terminals**:
  ```
  python -m window_tiler.main terminals --rows 1 --columns 3
  ```

#### Options

- `--rows N`: Number of rows in the grid layout
- `--columns N`: Number of columns in the grid layout
- `--monitor N`: Index of monitor to use (0 is primary)

### API Usage

#### Working with Monitors

```python
from window_tiler.core import get_all_monitors

# Get all monitors
monitors = get_all_monitors()

# Get primary monitor
primary_monitor = next((m for m in monitors.values() if m.is_primary), None)

# Get monitor dimensions
dims = primary_monitor.get_dimensions()
print(f"Monitor dimensions: {dims['width']}x{dims['height']}")
```

#### Working with Windows

```python
from window_tiler.core import get_all_windows, get_windows_by_type, WindowType

# Get all windows
windows = get_all_windows()

# Get windows by type
browser_windows = get_windows_by_type(WindowType.BROWSER_CHROME)
explorer_windows = get_windows_by_type(WindowType.EXPLORER_NORMAL)

# Get windows by process name
chrome_windows = get_windows_by_type(process_name='chrome.exe')

# Get windows by class name
explorer_windows = get_windows_by_type(window_class='CabinetWClass')

# Manipulate a window
if browser_windows:
    window = browser_windows[0]
    window.bring_to_front()
    window.focus()
```

#### Using the Tiler

```python
from window_tiler.core import get_all_monitors, get_windows_by_type, Tiler, WindowType

# Get monitors and windows
monitors = get_all_monitors()
primary_monitor = next((m for m in monitors.values() if m.is_primary), None)
browser_windows = get_windows_by_type(WindowType.BROWSER_CHROME)

# Create tiler
tiler = Tiler(monitors)

# Tile windows in a grid
tiler.tile_grid(primary_monitor, browser_windows, rows=2, columns=2)

# Tile with custom ratios
column_ratios = [0.6, 0.4]  # 60%, 40%
row_ratios = [0.5, 0.5]     # 50%, 50%
tiler.tile_grid(primary_monitor, browser_windows, rows=2, columns=2, 
               column_ratios=column_ratios, row_ratios=row_ratios)

# Use split layout
if len(browser_windows) > 1:
    primary_window = browser_windows[0]
    secondary_windows = browser_windows[1:]
    tiler.split(primary_monitor, primary_window, secondary_windows, 
               primary_ratio=0.7, orientation='vertical')
```

## Advanced Topics

### Custom Window Type Detection

You can extend the window type detection system by modifying the `determine_window_type` function in `utils.py`. For example, to add a new application type:

```python
# In utils.py
def determine_window_type(hwnd):
    # Existing code...
    
    # Add custom detection logic
    if process_name and 'yourapp.exe' in process_name.lower():
        return WindowType.YOUR_APP_TYPE
    
    # Rest of the function...
```

### Adding New Window Types

To add new window types, extend the `WindowType` enum in `core/types.py`:

```python
# In core/types.py
class WindowType(Enum):
    # Existing types...
    
    # Add your custom types
    YOUR_APP_TYPE = auto()
    ANOTHER_APP_TYPE = auto()
```

### Implementing Custom Tiling Strategies

You can implement custom tiling strategies by extending the `Tiler` class in `core/tiler.py`:

```python
# In core/tiler.py
class Tiler:
    # Existing methods...
    
    def your_custom_layout(self, monitor, windows, **options):
        """
        Implement your custom layout strategy.
        """
        # Your layout logic here
```

## Troubleshooting

### Common Issues

1. **Windows Not Detected**
   - Ensure windows are visible and have titles
   - Check if windows are running with elevated privileges

2. **Windows Not Responding to Tiling**
   - Some windows (like Task Manager) may resist being moved or resized
   - Check if windows are in a special state (minimized, maximized)

3. **Type Detection Not Working**
   - Ensure the process detection has necessary permissions
   - Some processes may block access to their information

### Debugging Tips

1. Use the `list` command with `--types` and `--classes` to see detailed window information
2. Check process access permissions for security-sensitive applications
3. Use detailed logs by adding `print` statements to track window operations

## Conclusion

Window Tiler provides a powerful yet simple way to organize windows based on their type. By combining the command-line interface for quick operations and the Python API for custom solutions, you can create efficient window layouts that match your workflow.

For practical examples, see the `examples.md` file.
