:: =============================================================================
:: cmd: initialize
:: =============================================================================
@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
SET "__init_path__=%CD%"
SET "__base_name__=%~n0"
SET "__venv_name__=venv"


:: =============================================================================
:: venv: locate
:: =============================================================================
SET "__venv_identifier__=%__venv_name__%\Scripts\python.exe"
:LocateVenv
    IF EXIST "%CD%\%__venv_identifier__%" (GOTO ActivateVenv)
    SET "tmp=%CD%" & CD .. & IF "%CD%"=="%tmp%" (
        ECHO Not found: %__venv_identifier__%
        ECHO make sure you've initialized the venv.
        CD /D "%__init_path__%"
        PAUSE>NUL & EXIT /B
    )
GOTO LocateVenv


:: =============================================================================
:: venv: activate
:: =============================================================================
:ActivateVenv
    SET "__venv_stem__=%CD%"
    CD /D "%__init_path__%"
    CALL "%__venv_stem__%\%__venv_name__%\Scripts\activate"
    ::
    ECHO __init_path__: %__init_path__%
    ECHO __venv_stem__: %__venv_stem__%
    ECHO.
    GOTO ExecuteCommand


:ExecuteCommand
    :: =============================================================================
    :: requirements.txt: upgrade
    :: =============================================================================
    SET requirements_txt="%__venv_stem__%\requirements.txt"
    IF EXIST "%requirements_txt%" (
        ECHO. & ECHO Do you want to upgrade the packages? ^(press 'y' to upgrade...^)
        SET /P upgrade_packages=">> "
        IF "!upgrade_packages!"=="y" (
            "python" -m pip install --upgrade pip
            "python" -m pip install --upgrade -r "%requirements_txt%" -vvv
            IF NOT ERRORLEVEL 1 ("pip" freeze > "%requirements_txt%") ELSE (
                ECHO Failed to upgrade packages from "%requirements_txt%"
            )
        )
    )

    :: =============================================================================
    :: requirements.txt: write
    :: =============================================================================
    SET requirements_txt="%__venv_stem__%\requirements.txt"
    SET requirements_txt="%__venv_stem__%\requirements.txt"
    IF NOT ERRORLEVEL 1 ("pip" freeze > %requirements_txt%) ELSE (
        ECHO wailed to write  %requirements_txt%
    )


:: =============================================================================
:: cmd: exit
:: =============================================================================
ECHO. & ECHO Window will close in 10 seconds ...& PING 127.0.0.1 -n 10 > NUL
EXIT /B