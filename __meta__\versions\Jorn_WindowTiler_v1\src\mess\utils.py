import json

def write_json(json_data, json_path, indent=4, encoding="utf-8", mode="w+"):
    """
    Write JSON data to a file.

    Args:
        json_data (dict): Data to be written in JSON format.
        json_path (str): Path to the JSON file.
        indent (int): Indentation level for the JSON file.
        encoding (str): Encoding format.
        mode (str): File opening mode.
    """
    with open(json_path, mode, encoding=encoding) as json_file:
        json.dump(json_data, json_file, indent=indent)


