# Window Tiler

A utility for dynamically arranging windows on Windows operating systems based on their type (process name, window class), enabling efficient workspace organization across multiple monitors.

## Features

- **Type-Based Window Tiling**: Arrange windows by process name, window class, or predefined types
- **Multi-Monitor Support**: Work with multiple displays and target specific monitors
- **Customizable Grid Layouts**: Define rows, columns, and size ratios for window arrangements
- **Advanced Window Filtering**: Intelligent window detection and categorization
- **Simple Command Interface**: Easy-to-use command line tools for common operations

## Installation

1. Ensure Python 3.7+ is installed
2. Install requirements:
```
pip install pywin32
```
3. Clone or download this repository

## Usage

### Command Line Interface

Basic list of windows:
```
python -m window_tiler.main list
```

Tiling by process name:
```
python -m window_tiler.main process chrome.exe --rows 2 --columns 2
```

Tiling all browser windows:
```
python -m window_tiler.main browsers
```

Show all available monitors:
```
python -m window_tiler.main monitors
```

### Python API

```python
from window_tiler.core import get_all_monitors, get_windows_by_type, Tiler, WindowType

# Get monitors and windows
monitors = get_all_monitors()
primary_monitor = next(m for m in monitors.values() if m.is_primary)

# Get all Chrome windows
chrome_windows = get_windows_by_type(WindowType.BROWSER_CHROME)

# Create tiler and arrange windows in 2x2 grid
tiler = Tiler(monitors)
tiler.tile_grid(primary_monitor, chrome_windows, rows=2, columns=2)
```

## Structure

The project follows a minimalist but powerful structure:

```
window_tiler/
├── core/                # Core components
│   ├── __init__.py     # Package exports
│   ├── monitor.py      # Monitor detection/representation
│   ├── window.py       # Window detection/manipulation
│   ├── tiler.py        # Window tiling algorithms
│   └── types.py        # Type enumeration
├── docs/               # Documentation
├── main.py             # Command line interface
├── utils.py            # Utility functions
└── README.md           # Project overview
```

## Key Components

- **Monitor**: Represents physical displays with boundaries and work areas
- **Window**: Represents application windows with enhanced properties and type detection
- **Tiler**: Implements window arrangement algorithms
- **Types**: Defines window type classifications
- **Utils**: Provides utility functions for shell window and process handling

## Window Types

The system can detect and categorize windows by:

- Process name (e.g., chrome.exe)
- Window class name (e.g., CabinetWClass)
- Predefined types:
  - Browsers (Chrome, Edge, Firefox)
  - Explorer windows (normal folders, special folders)
  - Terminals (cmd, PowerShell)
  - Document editors
  - And more

## License

MIT
