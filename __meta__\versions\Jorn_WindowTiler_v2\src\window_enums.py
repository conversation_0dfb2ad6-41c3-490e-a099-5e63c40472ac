"""
Window Enumerations for the Window Tiler application.

This module defines the enumeration types used for classifying and identifying windows
based on their type, process, or other characteristics.
"""

from enum import Enum, auto


class WindowType(Enum):
    """
    Enum for classifying windows based on their type, process, or characteristics.
    Used for filtering windows in tiling operations.
    """
    # Basic window types
    UNSPECIFIED = auto()  # Default type when no specific type can be determined
    NORMAL = auto()       # Standard application window
    SYSTEM = auto()       # System windows (Task Manager, etc.)
    TOOL = auto()         # Utility windows, toolbars, etc.
    UNKNOWN = auto()      # Unknown window type
    UNLINKED = auto()     # Window without associated process

    # Explorer specific types
    EXPLORER_NORMAL = auto()        # Normal file explorer window
    EXPLORER_SPECIAL_FOLDER = auto()  # Special folder explorer window (This PC, Control Panel, etc.)
    
    # Browser types
    BROWSER = auto()      # Generic browser window
    BROWSER_CHROME = auto()  # Chrome browser window
    BROWSER_EDGE = auto()    # Edge browser window
    BROWSER_FIREFOX = auto() # Firefox browser window
    
    # Application specific types
    TERMINAL = auto()     # Terminal window (cmd, PowerShell, etc.)
    EDITOR = auto()       # Text editor window
    IDE = auto()          # Integrated development environment
    DOCUMENT = auto()     # Document window (Word, PDF viewer, etc.)
    MEDIA = auto()        # Media player window


class WindowState(Enum):
    """
    Enum for representing the current state of a window.
    """
    NORMAL = auto()      # Window is in normal state
    MINIMIZED = auto()   # Window is minimized
    MAXIMIZED = auto()   # Window is maximized
    FULLSCREEN = auto()  # Window is in fullscreen mode
    HIDDEN = auto()      # Window is hidden


class WindowExplorerType(Enum):
    """
    Enum for specifically classifying Explorer windows.
    """
    NORMAL = auto()         # Regular folder window
    SPECIAL_FOLDER = auto()  # Special folder (This PC, Control Panel, etc.)
    SEARCH = auto()         # Search results window
    UNKNOWN = auto()        # Unknown explorer window type


class WindowCategory(Enum):
    """
    Enum for high-level categorization of windows.
    """
    APPLICATION = auto()  # Standard application window
    SYSTEM = auto()       # System window
    UTILITY = auto()      # Utility/tool window
    DIALOG = auto()       # Dialog window
    OTHER = auto()        # Other window type
